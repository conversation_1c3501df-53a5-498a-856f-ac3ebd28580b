import type { TaskConfig, TaskHandlerArgs } from 'payload'

function isNextMonthWithinSubscriptionPeriod(now: Date, endDateString: string): boolean {
  const endDate = new Date(endDateString)
  const oneMonthLater = now
  oneMonthLater.setMonth(now.getMonth() + 1)

  // Check if one month + current time is before the subscription period end
  return oneMonthLater <= endDate
}

function determineNextReleaseDate(startDateString: string, lastCreditReleaseString: string): Date {
  const lastCreditRelease = new Date(lastCreditReleaseString)
  const monthOfLastCreditRelease = lastCreditRelease.getMonth()
  const yearOfLastCreditRelease = lastCreditRelease.getFullYear()
  // use this to set date and time, then set last credit release year and month + 1
  // use start date and time to prevent accumulated shifting forward
  // since making use of lastReleaseDate, it's important to update lastReleaseDate correctly (at least month/year need to be accurate)
  const nextReleaseDate = new Date(startDateString)
  nextReleaseDate.setFullYear(yearOfLastCreditRelease)
  nextReleaseDate.setMonth(monthOfLastCreditRelease + 1)

  return nextReleaseDate
}

function isWithin24HoursOfNextReleaseDate(now: Date, nextReleaseDate: Date): boolean {
  const twentyFourHoursBefore = new Date(nextReleaseDate.getTime() - 24 * 60 * 60 * 1000)

  return now >= twentyFourHoursBefore && now <= nextReleaseDate
}

// Separate handler function
const releaseCreditHandler = async ({ req }: TaskHandlerArgs<'ReleaseCreditAnnualPlan'>) => {
  console.log('Releasing annual plan credits...')

  const subscriptions = await req.payload.find({
    collection: 'subscription-records',
    depth: 2,
    where: {
      and: [
        {
          status: {
            equals: 'active',
          },
        },
        {
          'subscriptionPlan.subscription_duration': {
            equals: 'year',
          },
        },
      ],
    },
  })

  const now = new Date() // Consistent timestamp for all checks
  const releasedToUsers: string[] = []
  for (const subscription of subscriptions.docs) {
    const nextReleaseDate = determineNextReleaseDate(
      subscription.currentPeriodStart,
      subscription.lastCreditRelease!,
    )
    // Check both conditions with consistent 'now' timestamp
    if (
      isNextMonthWithinSubscriptionPeriod(now, subscription.currentPeriodEnd) &&
      isWithin24HoursOfNextReleaseDate(now, nextReleaseDate)
    ) {
      // Get the user ID - subscription.user could be a string or populated object
      const userId =
        typeof subscription.user === 'string' ? subscription.user : subscription.user?.id

      if (userId) {
        // TODO confirm rollover credits for annual plan
        // Get current user credits and subscription plan credits
        const currentCredits =
          typeof subscription.user === 'object' ? subscription.user!.paidCredits : 0
        const creditsToAdd =
          typeof subscription.subscriptionPlan === 'object'
            ? subscription.subscriptionPlan!.monthlyCreditStipend
            : 0

        // Release credits
        await req.payload.update({
          collection: 'public-users',
          id: userId,
          data: {
            paidCredits: (currentCredits || 0) + creditsToAdd,
          },
        })
        await req.payload.update({
          collection: 'subscription-records',
          id: subscription.id,
          data: {
            lastCreditRelease: nextReleaseDate.toISOString(),
          },
        })
        releasedToUsers.push(userId)
      }
    }
  }

  return {
    output: {
      success: true,
      message: 'Credits released successfully',
      timestamp: new Date().toISOString(),
      usersProcessed: releasedToUsers,
    },
  }
}

export const ReleaseCreditAnnualPlan: TaskConfig<'ReleaseCreditAnnualPlan'> = {
  slug: 'ReleaseCreditAnnualPlan',
  schedule: [
    {
      cron: '0 0 * * *',
      queue: 'nightly',
    },
  ],
  handler: releaseCreditHandler,
}
