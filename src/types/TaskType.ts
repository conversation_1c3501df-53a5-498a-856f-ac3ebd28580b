/**
 * Enum representing the different types of image generation tasks.
 */
export enum TaskType {
  /**
   * Text-to-Image generation
   */
  TEXT_TO_IMAGE = 't2i',

  /**
   * Image-to-Image generation
   */
  IMAGE_TO_IMAGE = 'i2i',

  /**
   * Background removal
   */
  REMOVE_BACKGROUND = 'rmbg',

  /**
   * Upscaling
   */
  UPSCALE = 'upscale',

  /**
   * Contrast adjustment
   */
  CONTRAST = 'contrast',
}

export enum TaskTypeDisplayName {
  't2i' = 'Text to Image',
  'i2i' = 'Image to Image',
  'rmbg' = 'Background Removal',
  'upscale' = 'Upscaling',
  'contrast' = 'Contrast',
}
