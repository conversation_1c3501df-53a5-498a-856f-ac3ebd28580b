import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "subscription_records" RENAME COLUMN "subscription_id" TO "stripe_subscription_id";
  ALTER TABLE "subscription_records" RENAME COLUMN "customer_id" TO "stripe_customer_id";
  ALTER TABLE "subscription_records" ADD COLUMN "last_credit_release" timestamp(3) with time zone;`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "subscription_records" RENAME COLUMN "stripe_subscription_id" TO "subscription_id";
  ALTER TABLE "subscription_records" RENAME COLUMN "stripe_customer_id" TO "customer_id";
  ALTER TABLE "subscription_records" DROP COLUMN IF EXISTS "last_credit_release";`)
}
