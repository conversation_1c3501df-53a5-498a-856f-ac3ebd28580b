import type { CollectionConfig, Description } from 'payload/'
import { Category } from '@/payload-types'
import CategoryIconDescription from '@/payloadCMS/components/CategoryIconDescription'

export function isCategories(
  category: (number | Category)[] | null | undefined,
): category is Category[] {
  return typeof category === 'object' && category !== null
}

export function isCategory(category: (number | Category) | null | undefined): category is Category {
  return typeof category === 'object' && category !== null
}

export interface Type {
  id: string
  name: string
  archived: string
  createdAt: string
  updatedAt: string
}

const Categories: CollectionConfig = {
  slug: 'categories',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'id', 'archived'],
    group: 'Content',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      localized: true,
    },
    {
      name: 'icon',
      type: 'text',
      admin: {
        description: CategoryIconDescription as unknown as Description,
      },
    },
    {
      name: 'archived',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Archiving filters it from being an option in the posts collection',
      },
    },
    {
      name: 'summary',
      // ui fields do not impact the database or api, and serve as placeholders for custom components in the admin panel
      type: 'ui',
      admin: {
        position: 'sidebar',
        components: {
          // this custom component will fetch the posts count for how many posts have this category
          Field: '../payloadCMS/components/CategorySummary',
        },
      },
    },
  ],
}

export default Categories
