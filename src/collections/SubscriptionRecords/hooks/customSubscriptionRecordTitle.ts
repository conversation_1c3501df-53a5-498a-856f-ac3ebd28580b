import { FieldHook } from 'payload'

const getCustomTitleForSubscriptionRecord =
  async (
    user: string,
    subscriptionPlan: string,
    startDate: string,
    stripeSubscriptionId: string,
  ): Promise<FieldHook> =>
  async ({ value, originalDoc, data, req }) => {
    if (value) {
      return value
    }

    //Use this to fill in the values: req.payload.find
    // const userPayload = await req.payload.findByID({
    //   collection: 'public-users',
    //   id: user
    // })

    // const subscriptionPayload = await req.payload.findByID({
    //   collection: 'subscription-plans',
    //   id: subscriptionPlan
    // })

    // console.log(userPayload)
    // console.log(subscriptionPayload)

    const fallbackUserData = data?.[user] || originalDoc?.[user]
    const fallbackSubscriptionData = data?.[subscriptionPlan] || originalDoc?.[subscriptionPlan]
    const fallbackStripeSubscriptionId =
      data?.[stripeSubscriptionId] || originalDoc?.[stripeSubscriptionId]

    const constructText = `${fallbackStripeSubscriptionId} - ${fallbackUserData} - ${fallbackSubscriptionData}`

    return constructText
  }

export default getCustomTitleForSubscriptionRecord
