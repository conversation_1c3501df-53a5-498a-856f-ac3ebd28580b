import { CollectionConfig } from 'payload'
import isFieldAdminOnly from '@/payloadCMS/roleaccess/isFieldAdminOnly'
import getCustomTitleForSubscriptionRecord from './hooks/customSubscriptionRecordTitle'

//This collection is purely for record keeping purposes. DO NOT EDIT.
//Editing is only allowed for cases where you are manually creating a subscription for a user.
//If manually assigning a subscription for user, please add a corresponding record in this collection
//Please also explain why in the comments section. Again for record keeping purposes in disputes.

const SubscriptionRecord: CollectionConfig = {
  slug: 'subscription-records',
  admin: {
    useAsTitle: 'customTitle',
    group: 'Payment',
  },
  fields: [
    {
      name: 'stripeSubscriptionId',
      label: 'Stripe Subscription ID',
      admin: {
        description:
          'The ID of the subscription in Stripe. This is used to link the subscription record with Stripe.',
      },
      type: 'text',
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'public-users',
      index: true,
      hasMany: false,
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'subscriptionPlan',
      type: 'relationship',
      relationTo: 'subscription-plans',
      hasMany: false,
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'stripeCustomerId',
      type: 'text',
      label: 'Stripe Customer ID',
      admin: {
        description:
          'The ID of the customer in Stripe. This is used to link the subscription record with Stripe.',
      },
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Canceled', value: 'canceled' },
        { label: 'Past Due', value: 'past_due' },
        { label: 'Incomplete', value: 'incomplete' },
        { label: 'Incomplete Expired', value: 'incomplete_expired' },
        { label: 'Trialing', value: 'trialing' },
        { label: 'Unpaid', value: 'unpaid' },
        { label: 'Paused', value: 'paused' },
      ],
      required: true,
      defaultValue: 'incomplete',
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'currentPeriodStart',
      label: 'Current Period Start',
      type: 'date',
      required: true,
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'currentPeriodEnd',
      label: 'Current Period End',
      type: 'date',
      required: true,
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'cancelAt',
      label: 'Cancel At',
      type: 'date',
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'canceledAt',
      label: 'Canceled At',
      type: 'date',
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'createdAt',
      type: 'date',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [() => new Date()],
      },
    },
    {
      name: 'updatedAt',
      type: 'date',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [() => new Date()],
      },
    },
    {
      name: 'lastCreditRelease',
      label: 'Last Credit Release',
      type: 'date',
      admin: {
        description: 'The last time credits were released to the user for this subscription',
      },
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'stripeComment',
      label: 'Stripe Comment',
      type: 'textarea',
      admin: {
        description: 'Strictly for stripe, mainly to check reasons for possible payment failures',
      },
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'comments',
      label: 'Admin Comments',
      type: 'textarea',
      admin: {
        description:
          'PLEASE explain why this subscription record was added / edited if manually added / edited. If it is a dispute, PLEASE add the header of the email chain or the STRIPE dispute id. Or any other link references if external.',
      },
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'customTitle',
      label: 'Custom Generated Title - DO NOT OVERWRITE!',
      type: 'text',
      admin: {
        hidden: true, // Hide this field in the UI (only used for title)
      },
      access: {
        read: () => true,
      },
      hooks: {
        beforeChange: [
          await getCustomTitleForSubscriptionRecord(
            'user',
            'subscriptionPlan',
            'startDate',
            'stripeSubscriptionId',
          ),
        ],
      },
    },
  ],
}

export default SubscriptionRecord
