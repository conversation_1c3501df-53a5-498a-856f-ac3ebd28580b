import type { MetadataRoute } from 'next'
import { getPayload } from 'payload'
import config from '@payload-config'
import CategoryDAO from './(app)/_backend/common/dao/CategoryDAO'
import PostDAO from './(app)/_backend/common/dao/PostDAO'
import CategoryService from './(app)/_backend/common/service/CategoryService'
import PostService from './(app)/_backend/common/service/PostService'

const BASE_URL = process.env.NEXT_PUBLIC_HOST

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const finalSitemap = [] as MetadataRoute.Sitemap
  const payload = await getPayload({ config })
  const categoryDAO = new CategoryDAO(payload)
  const categoryService = new CategoryService(categoryDAO)
  const postDAO = new PostDAO(payload)
  const postService = new PostService(postDAO)
  const posts = await postService.getAllPostsSlugSitemap()
  const categories = await categoryService.getAllCategories()

  const staticPages = ['', 'pricing', 'register', 'login', 'reset-password'].map((slug) => ({
    url: `${BASE_URL}/${slug}`,
    lastModified: new Date().toISOString(),
    changeFrequency: 'monthly',
    priority: 0.8,
  })) as MetadataRoute.Sitemap

  finalSitemap.push(...staticPages)

  finalSitemap.push({
    url: `${BASE_URL}/blog`,
    lastModified: new Date().toISOString(),
    changeFrequency: 'daily',
    priority: 0.5,
  })

  categories.map((category) => {
    finalSitemap.push({
      url: `${BASE_URL}/blog/${category.name?.toLowerCase()}`,
      lastModified: new Date().toISOString(),
      changeFrequency: 'daily',
      priority: 0.5,
    })
  })

  posts.map((post) => {
    finalSitemap.push({
      url: `${BASE_URL}/blog/${post.category?.toLowerCase()}/${post.slug}`,
      lastModified: post.updatedAt,
      changeFrequency: 'monthly',
      priority: 0.5,
    })
  })

  return finalSitemap
}
