import { SubscriptionPlan } from '@/payload-types'

export interface SubscriptionPlanMinimalInfo {
  name: string
  description: string | null | undefined
  credits: number
  price: number
  subscription_duration: 'month' | 'day' | 'year' | 'week'
}

export class SubscriptionPlanDTO {
  subscriptionPlan: SubscriptionPlan
  constructor(subcriptionPlan: SubscriptionPlan) {
    this.subscriptionPlan = subcriptionPlan
  }

  distil() {
    return {
      id: this.subscriptionPlan.id,
      name: this.subscriptionPlan.name,
      description: this.subscriptionPlan.description,
      stripePriceId: this.subscriptionPlan.stripePriceId,
      credits: this.subscriptionPlan.monthlyCreditStipend,
      price: this.subscriptionPlan.price,
      subscription_duration: this.subscriptionPlan.subscription_duration,
    }
  }

  minimalInfo() {
    return {
      name: this.subscriptionPlan.name,
      description: this.subscriptionPlan.description,
      credits: this.subscriptionPlan.monthlyCreditStipend,
      price: this.subscriptionPlan.price,
      subscription_duration: this.subscriptionPlan.subscription_duration,
    }
  }
}
