import { StatusCodes } from 'http-status-codes'
import Stripe from 'stripe'
import { stripe } from '@/lib/stripe'
import { PublicUser, SubscriptionPlan } from '@/payload-types'
import SubscriptionPlanDAO from '../../dao/SubscriptionPlanDAO'
import SubscriptionRecordsDAO from '../../dao/SubscriptionRecordsDAO'
import User<PERSON><PERSON> from '../../dao/UserDAO'
import { HTTPException } from '../../exception'

class StripeService {
  private subscriptionPlanDAO: SubscriptionPlanDAO
  private publicUserDAO: UserDAO
  private subscriptionRecordDAO: SubscriptionRecordsDAO

  private MINIMUM_CREDIT_DISPENSE_COOLDOWN_PERIOD_IN_MINUTES = 60

  constructor(
    subscriptionPlanDAO: SubscriptionPlanDAO,
    publicUserDAO: UserDAO,
    subscriptionRecordDAO: SubscriptionRecordsDAO,
  ) {
    this.subscriptionPlanDAO = subscriptionPlanDAO
    this.publicUserDAO = publicUserDAO
    this.subscriptionRecordDAO = subscriptionRecordDAO
  }

  /**
   * Handles the successful completion of a Stripe checkout session by updating the subscription record.
   *
   * @param event - The event triggered by <PERSON><PERSON>, containing details of the completed checkout session.
   *
   * This function retrieves necessary details from the Stripe checkout session, such as the subscription ID,
   * user ID, and subscription record ID. It then fetches the subscription details from Stripe and updates the
   * subscription record in the database. If the payment status is 'paid', it marks the subscription as active
   * and updates the user's subscription record. If the payment status is 'unpaid', it retrieves the latest
   * invoice and payment intent details, logs any decline information, and updates the subscription record as
   * unpaid. It also handles cases where the session status is 'open' or 'expired' by returning a message
   * indicating the process was interrupted.
   *
   * @throws HTTPException if any required fields are empty.
   *
   * @returns An object containing a message and a boolean indicating whether the payment was successful.
   */

  async updateSubscriptionRecordOnCheckoutSessionSuccess(
    event: Stripe.CheckoutSessionCompletedEvent,
  ) {
    const session = event.data.object
    const clientReferenceId = session.client_reference_id //reference to the user id
    const stripeSubscriptionId = session.subscription as string | null
    if (!session.metadata || !stripeSubscriptionId || !clientReferenceId) {
      throw new HTTPException('Empty fields', StatusCodes.BAD_REQUEST)
    }
    const subscriptionRecordId = parseInt(session.metadata.subscriptionRecordId)
    const stripePriceId = session.metadata.stripePriceId
    const fetchSubscription = await stripe.subscriptions.retrieve(stripeSubscriptionId)
    const customer = session.customer as string | null
    const startDate = fetchSubscription.items.data[0].current_period_start
    const endDate = fetchSubscription.items.data[0].current_period_end

    const subscriptionPlan =
      await this.subscriptionPlanDAO.retrieveSubscriptionByPriceId(stripePriceId)

    const lastCreditRelease = new Date().toISOString()

    if (session.payment_status === 'paid') {
      await this.subscriptionRecordDAO.genericUpdateSubscriptionRecord(subscriptionRecordId, {
        stripeSubscriptionId: stripeSubscriptionId,
        stripeCustomerId: customer,
        subscriptionPlan: subscriptionPlan.id,
        user: clientReferenceId,
        status: 'active',
        currentPeriodStart: new Date(startDate * 1000).toISOString(),
        currentPeriodEnd: new Date(endDate * 1000).toISOString(),
        lastCreditRelease,
      })
      await this.publicUserDAO.genericUpdateUser(clientReferenceId, {
        subscription: subscriptionRecordId,
        role: 'subscriber',
        freeCredits: subscriptionPlan.monthlyCreditStipend, // TODO: this means no rollover?
      })
      return {
        message: 'User has successfully paid for the subscription',
        received: true,
      }
    } else if (session.payment_status === 'unpaid') {
      const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId, {
        expand: ['latest_invoice.payments.data.payment_intent'],
      })
      if (subscription.latest_invoice && typeof subscription.latest_invoice !== 'string') {
        const invoice = subscription.latest_invoice

        if (invoice.payments && invoice.payments.data.length > 0) {
          const payment = invoice.payments.data[0]
          if (payment.payment && payment.payment.type === 'payment_intent') {
            const paymentIntent = payment.payment.payment_intent
            if (typeof paymentIntent !== 'string' && paymentIntent != undefined) {
              const paymentIntentStatus = paymentIntent.status
              const paymentIntentErrorDeclineCode = paymentIntent.last_payment_error?.decline_code
              const paymentIntentErrorDeclineMessage = paymentIntent.last_payment_error?.message
              const paymentIntentErrorDeclineType = paymentIntent.last_payment_error?.type
              const paymentIntentErrorDeclineDocUrl = paymentIntent.last_payment_error?.doc_url
              const resStr = `
                    Status: ${paymentIntentStatus}
                    Decline Code: ${paymentIntentErrorDeclineCode}
                    Decline Message: ${paymentIntentErrorDeclineMessage}
                    Decline Type: ${paymentIntentErrorDeclineType}
                    Decline Doc Url: ${paymentIntentErrorDeclineDocUrl}
                  `
              await this.subscriptionRecordDAO.genericUpdateSubscriptionRecord(
                subscriptionRecordId,
                {
                  stripeSubscriptionId: stripeSubscriptionId,
                  stripeCustomerId: customer,
                  subscriptionPlan: subscriptionPlan.id,
                  user: clientReferenceId,
                  status: 'unpaid',
                  currentPeriodStart: new Date(startDate * 1000).toISOString(),
                  currentPeriodEnd: new Date(endDate * 1000).toISOString(),
                  lastCreditRelease,
                  stripeComment: resStr,
                  canceledAt: new Date().toISOString(),
                },
              )
            }
          }
        } else {
          console.info('No payments found on the latest invoice')
        }
      } else {
        console.info('Latest invoice not available or not expanded')
      }
      return {
        message: 'Unpaid',
        received: true,
      }
    } else if (session.status === 'open' || session.status === 'expired') {
      return {
        message: 'Process interupted',
        received: true,
      }
    }

    return {
      message: 'Something unexpected happened',
      received: false,
    }
  }

  /**
   * This method is called when an invoice is paid.
   *
   * It updates the subscription record with the new information from the invoice.
   *
   * If we cannot find an existing subscription record, we will assume this webhook is triggered by a new subscription.
   *
   * Proceed to ignore it if that's the case as it's handled by checkout session success webhook.
   */
  async updateSubscriptionRecordOnInvoicePaid(event: Stripe.InvoicePaidEvent) {
    console.log("'-- INVOICE PAID EVENT RECEIVED --'")
    const invoice = event.data.object
    let bCreditDispensed = false
    const stripeSubscriptionId = invoice.parent?.subscription_details?.subscription as string | null
    if (!stripeSubscriptionId) {
      //SKIP: If the invoice does not have a subscription, we cannot update the subscription record
      return { message: 'No subscription found in the invoice', received: true }
    }
    const subscriptionRecord =
      await this.subscriptionRecordDAO.retrieveSubscriptionRecordBySubscriptionId(
        stripeSubscriptionId,
      )

    if (!subscriptionRecord) {
      //SKIP: If we don't have a subscription record, we'll assume it's a new subscription, and ignore.
      return { message: 'No subscription found in the invoice', received: true }
    }

    const subscriptionUser = subscriptionRecord.user as PublicUser | null
    const subscriptionPlan = subscriptionRecord.subscriptionPlan as SubscriptionPlan | null

    if (!subscriptionPlan || !subscriptionUser) {
      throw new HTTPException('Subscription plan not found', StatusCodes.NOT_FOUND)
    }

    const stripeSubscription = await stripe.subscriptions.retrieve(stripeSubscriptionId)
    const getCurrentPeriodStart = stripeSubscription.items.data[0].current_period_start

    // Update the subscription record with the new information from the invoice
    const dateNow = getCurrentPeriodStart * 1000
    const lastCreditRelease = subscriptionRecord.lastCreditRelease

    if (lastCreditRelease) {
      const lastCreditReleaseDate = new Date(lastCreditRelease).getTime()
      const minutesSinceLastRelease = Math.floor((dateNow - lastCreditReleaseDate) / (1000 * 60))

      if (minutesSinceLastRelease > this.MINIMUM_CREDIT_DISPENSE_COOLDOWN_PERIOD_IN_MINUTES) {
        //If number of minutes since last credit release is greater than the minimum cooldown period, update the last credit release date and release credits
        //Else we do not update to stay idempotent
        await this.publicUserDAO.genericUpdateUser(subscriptionUser.id, {
          subscription: subscriptionRecord.id,
          role: 'subscriber',
          freeCredits: subscriptionPlan.monthlyCreditStipend, // TODO: this means no rollover?
        })
        bCreditDispensed = true
      }
    } else {
      //If last credit release is not set, we assume this is the first time we are
      await this.publicUserDAO.genericUpdateUser(subscriptionUser.id, {
        subscription: subscriptionRecord.id,
        role: 'subscriber',
        freeCredits: subscriptionPlan.monthlyCreditStipend, // TODO: this means no rollover?
      })
      bCreditDispensed = true
    }
    this.subscriptionRecordDAO.genericUpdateSubscriptionRecord(subscriptionRecord.id, {
      status: 'active',
      lastCreditRelease: bCreditDispensed
        ? new Date().toISOString()
        : subscriptionRecord.lastCreditRelease,
    })

    return {
      message: 'Subscription record updated successfully',
      received: true,
    }
  }

  async updateCustomerSubscriptionOnSubscriptionUpdated(
    event: Stripe.CustomerSubscriptionUpdatedEvent,
  ) {
    const subscription = event.data.object
    const stripeSubscriptionId = subscription.id

    const subscriptionRecord =
      await this.subscriptionRecordDAO.retrieveSubscriptionRecordBySubscriptionId(
        stripeSubscriptionId,
      )
    if (!subscriptionRecord) {
      // If we don't have a subscription record, we'll assume it's a new subscription, and ignore.
      return { message: 'No subscription found for the updated customer', received: true }
    }

    if (subscriptionRecord.status === 'canceled') {
      // If the subscription is already canceled, we do not need to update it again.
      return { message: 'Subscription already canceled', received: true }
    }

    const status = subscription.status

    // Update the subscription record with the new information from the customer subscription update
    await this.subscriptionRecordDAO.genericUpdateSubscriptionRecord(subscriptionRecord.id, {
      currentPeriodStart: new Date(
        subscription.items.data[0].current_period_start * 1000,
      ).toISOString(),
      currentPeriodEnd: new Date(
        subscription.items.data[0].current_period_end * 1000,
      ).toISOString(),
      status: status,
    })

    return {
      message: 'Subscription record updated successfully on customer subscription update',
      received: true,
    }
  }

  async updateSubscriptionRecordOnPaymentFailed(event: Stripe.InvoicePaymentFailedEvent) {
    const invoice = event.data.object
    const stripeSubscriptionId = invoice.parent?.subscription_details?.subscription as string | null
    if (!stripeSubscriptionId) {
      //SKIP: If the invoice does not have a subscription, we cannot update the subscription record
      return { message: 'No subscription found in the invoice', received: true }
    }

    const subscriptionRecord =
      await this.subscriptionRecordDAO.retrieveSubscriptionRecordBySubscriptionId(
        stripeSubscriptionId,
      )

    if (!subscriptionRecord) {
      //SKIP: If we don't have a subscription record, we assume a new user is trying to purchase a NEW subscription.
      //We ignore this failure as it's handled by checkout session.
      return { message: 'No subscription found in the invoice', received: true }
    }

    if (subscriptionRecord.status === 'canceled') {
      // If the subscription is already canceled, we do not need to update it again.
      return { message: 'Subscription already canceled', received: true }
    }

    const subscriptionRecordId = subscriptionRecord.id
    await this.subscriptionRecordDAO.genericUpdateSubscriptionRecord(subscriptionRecordId, {
      status: 'past_due',
      stripeComment: `Payment failed for invoice ${invoice.id} at: ${new Date().toISOString()}`,
    })

    //strip user of active subscription, but DON'T REMOVE from active subscription record yet.
    const subscriptionUser = subscriptionRecord.user as PublicUser | null
    if (!subscriptionUser) {
      throw new HTTPException('Subscription user not found', StatusCodes.NOT_FOUND)
    }

    await this.publicUserDAO.genericUpdateUser(subscriptionUser.id, {
      subscription: subscriptionRecordId,
      role: 'free',
      freeCredits: 0,
    })

    return {
      message: 'Subscription record updated to past_due on payment failure',
      received: true,
    }
  }

  async updateCustomerSubscriptionOnSubscriptionDeleted(
    event: Stripe.CustomerSubscriptionDeletedEvent,
  ) {
    const subscription = event.data.object
    const stripeSubscriptionId = subscription.id
    const stripeSubscriptionCanceledAt = subscription.canceled_at

    const subscriptionRecord =
      await this.subscriptionRecordDAO.retrieveSubscriptionRecordBySubscriptionId(
        stripeSubscriptionId,
      )
    if (!subscriptionRecord) {
      // If we don't have a subscription record, we assume a new user is trying to purchase a NEW subscription.
      // We ignore this failure as it's handled by checkout session.
      return { message: 'No subscription found for the deleted customer', received: true }
    }
    if (!stripeSubscriptionCanceledAt) {
      throw new HTTPException(
        'Stripe subscription canceled at timestamp is missing',
        StatusCodes.BAD_REQUEST,
      )
    }

    // Update the subscription record to mark it as canceled
    await this.subscriptionRecordDAO.genericUpdateSubscriptionRecord(subscriptionRecord.id, {
      status: 'canceled',
      canceledAt: new Date(stripeSubscriptionCanceledAt * 1000).toISOString(),
    })

    const subscriptionUser = subscriptionRecord.user as PublicUser
    await this.publicUserDAO.genericUpdateUser(subscriptionUser.id, {
      subscription: null,
      role: 'free',
      freeCredits: 0,
    })

    return {
      message: 'Subscription record updated successfully on customer subscription deletion',
      received: true,
    }
  }
}

export default StripeService
