import { StatusCodes } from 'http-status-codes'
import { SubscriptionRecord } from '@/payload-types'
import SubscriptionPlanDAO from '../../dao/SubscriptionPlanDAO'
import SubscriptionRecordsDAO from '../../dao/SubscriptionRecordsDAO'
import { NotFound, HTTPException } from '../../exception'

class SubscriptionService {
  private subscriptionPlanDAO: SubscriptionPlanDAO
  private subscriptionRecordsDAO: SubscriptionRecordsDAO
  constructor(
    subscriptionPlanDAO: SubscriptionPlanDAO,
    subscriptionRecordsDAO: SubscriptionRecordsDAO,
  ) {
    this.subscriptionPlanDAO = subscriptionPlanDAO
    this.subscriptionRecordsDAO = subscriptionRecordsDAO
  }

  async getAllSubscriptionPlans() {
    const subscriptionPlans = await this.subscriptionPlanDAO.getAllSubscriptionPlans()
    return subscriptionPlans.docs
  }

  async getSubscriptionPlanById(id: string) {
    const subscriptionPlan = await this.subscriptionPlanDAO.getSubscriptionPlanById(id)
    if (subscriptionPlan == null) {
      throw new NotFound('Subscription plan not found')
    }
    return subscriptionPlan
  }

  async updateSubscriptionPlan(id: string, data: any) {
    const result = await this.subscriptionPlanDAO.updateSubscriptionPlan(id, data)
    return result
  }

  async retrieveSubscriptionByName(
    subscriptionName: string,
    subscriptionDuration: string, //year / month
  ) {
    const result = await this.subscriptionPlanDAO.retrieveSubscriptionByName(
      subscriptionName,
      subscriptionDuration,
    )
    if (result == null) {
      throw new NotFound('Subscription by subscriptionName is not found')
    }
    return result
  }

  async retrieveSubscriptionByProductId(
    productId: string,
    subscriptionDuration: string, //year / month
  ) {
    const result = await this.subscriptionPlanDAO.retrieveSubscriptionByProductId(
      productId,
      subscriptionDuration,
    )
    return result
  }

  async retrieveSubscriptionByPriceId(priceId: string) {
    const result = await this.subscriptionPlanDAO.retrieveSubscriptionByPriceId(priceId)
    if (result == null) {
      throw new NotFound('Subscription cannot be found')
    }
    return result
  }

  async addSubscription(
    stripeSubscriptionId: string | null = null,
    userId: string,
    subscriptionPlanId: string,
    stripeCustomerId: string,
    status: string,
    currentPeriodStart: number,
    currentPeriodEnd: number,
    stripeComment: string,
    cancelAt: number | null = null,
    canceledAt: number | null = null,
  ) {
    return this.subscriptionRecordsDAO.addSubscription(
      stripeSubscriptionId,
      userId,
      subscriptionPlanId,
      stripeCustomerId,
      status,
      currentPeriodStart,
      currentPeriodEnd,
      stripeComment,
      cancelAt,
      canceledAt,
    )
  }

  async updateSubscription(
    id: number,
    stripeSubscriptionId: string,
    userId: string,
    subscriptionPlanId: string,
    stripeCustomerId: string,
    status: string,
    currentPeriodStart: number,
    currentPeriodEnd: number,
    stripeComment: string,
    cancelAt: number | null = null,
    canceledAt: number | null = null,
    lastCreditRelease: string | null = null,
  ) {
    return this.subscriptionRecordsDAO.updateSubscriptionRecord(
      id,
      stripeSubscriptionId,
      userId,
      subscriptionPlanId,
      stripeCustomerId,
      status,
      currentPeriodStart,
      currentPeriodEnd,
      stripeComment,
      cancelAt,
      canceledAt,
      lastCreditRelease,
    )
  }

  /** **Should not be exposed to the public API, used internally** */
  async updateSubscriptionRecordGeneric(id: number, data: Partial<SubscriptionRecord>) {
    return this.subscriptionRecordsDAO.genericUpdateSubscriptionRecord(id, data)
  }

  /**
   * Retrieves a subscription record from the database by its stripe subscription id.
   * @param stripeSubscriptionId the id of the subscription in stripe
   * @returns the subscription record
   * @throws {HTTPException} if the subscription record is not found
   */
  async retrieveSubscriptionRecordBySubscriptionId(
    stripeSubscriptionId: string,
  ): Promise<SubscriptionRecord> {
    const subscriptionRecord =
      await this.subscriptionRecordsDAO.retrieveSubscriptionRecordBySubscriptionId(
        stripeSubscriptionId,
      )

    if (subscriptionRecord == null) {
      throw new HTTPException(`Subscription record not found`, StatusCodes.NOT_FOUND)
    }

    return subscriptionRecord
  }
  /**
   * Retrieves a subscription record by its ID.
   *
   * @param {string} id - The unique identifier of the subscription record.
   * @returns {Promise<SubscriptionRecord>} A promise that resolves to the subscription record.
   * @throws {HTTPException} Throws an error if the subscription record is not found.
   */

  async retrieveSubscriptionRecordById(id: string): Promise<SubscriptionRecord> {
    const subscriptionRecord = await this.subscriptionRecordsDAO.retrieveSubscriptionRecordById(id)

    if (subscriptionRecord == null) {
      throw new HTTPException(`Subscription record not found`, StatusCodes.NOT_FOUND)
    }

    return subscriptionRecord
  }
}

export default SubscriptionService
