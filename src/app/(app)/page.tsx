import {
  Circle<PERSON>rrowDown,
  CircleHelp,
  Dna,
  Library,
  Palette,
  Sparkles,
  TextIcon,
  Wallpaper,
  WandSparkles,
} from 'lucide-react'
import { Metadata } from 'next'
import NextImage from 'next/image'
import Link from 'next/link'
import React from 'react'

import { Spacing } from '@/utilities/local/css'
import { Button } from './_component/Button'
import { ShowcaseCompare } from './_component/ComparisonImage/ShowcaseCompare'
import { BackgroundLines } from './_component/Design/BackgroundLines'
import FourClover from './_component/Design/FourClover'
import { InfiniteMovingImages } from './_component/InfiniteMoving/Image'
import Text from './_component/Text'
import { ColourfulText } from './_component/Text/ColorfulText'
import { SparklesText } from './_component/Text/SparkleText'
import Title, { HeaderLevel } from './_component/Title'
import Container from './_cssComp/Container'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from './_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridContentAlignment, GridItem } from './_cssComp/GridContainer'
import MainFooter from './_templates/MainFooter'
import MainNavMenu from './_templates/MainNavMenu'

export const metadata: Metadata = {
  title: 'Free AI Coloring Page Generator - Start Your Trial Today | ColorAria',
  description:
    'Create unique coloring pages with AI! ColorAria lets you generate printable coloring sheets for kids, artists, and adults. Try it free today.',
  keywords: [
    'AI Coloring Page Generation',
    'AI Coloring',
    'Coloring Page for Kids',
    'Coloring Page for adults',
  ],
  openGraph: {
    title: 'Free AI Coloring Page Generator - Start Your Trial Today | ColorAria',
    description:
      'Create unique coloring pages with AI! ColorAria lets you generate printable coloring sheets for kids, artists, and adults. Try it free today.',
    siteName: 'ColorAria',
    type: 'website',
    url: `${process.env.NEXT_PUBLIC_HOST}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: `Free AI Coloring Page Generator - Start Your Trial Today | ColorAria`,
    site: `ColorAria`,
    description: `Create unique coloring pages with AI! ColorAria lets you generate printable coloring sheets for kids, artists, and adults. Try it free today.`,
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_HOST}`,
  },
}

const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

export default function Home() {
  return (
    <div className={`flex flex-col min-h-screen w-full`}>
      <div className="bg-main sticky top-0 w-full z-400">
        <MainNavMenu />
      </div>

      <GridContainer className={`w-full grid-cols-1`}>
        <GridItem colSpan={ColSpan.SPAN_1} className="relative ">
          <GridContainer
            contentAlign={GridContentAlignment.CENTER}
            contentJustify={GridContentAlignment.CENTER}
            className={`h-auto min-h-[50vh] w-full py-12 ${Spacing.ContentPadding} gap-8 bg-accent1`}
          >
            <GridItem colSpan={ColSpan.SPAN_12}>
              <div
                className={`${Spacing.ContentPadding} mx-auto px-4 max-w-full sm:max-w-[95%] md:max-w-[95%] lg:max-w-[90%] xl:max-w-[85%] 2xl:max-w-[65%]`}
              >
                <GridContainer className="w-full grid-cols-1 md:grid-cols-2 gap-16 relative">
                  <FlexContainer
                    className="w-full h-full gap-3 z-10"
                    justify={JustifyContent.CENTER}
                    align={AlignItems.START}
                    direction={FlexDirection.COL}
                  >
                    <Title level={HeaderLevel.H1} className="text-6xl stroke-black stroke-2">
                      <SparklesText sparklesCount={3}>AI</SparklesText>{' '}
                      <ColourfulText text="Coloring" /> Image Generator!
                    </Title>
                    <Text className="" variant="description" size="xl">
                      Let your imagination fly, and create your personal unique coloring page right
                      now!
                    </Text>
                    <Link href={`${HOST_URL}/register`} className="w-full">
                      <Button variant={'emphasis'} size="xl" className="mt-4 w-full">
                        <WandSparkles /> Get Started - for free!
                      </Button>
                    </Link>
                    <Text variant="description" size="xs" className="mt-2">
                      {`No credit card required. No strings attached. Just pure coloring fun!`}
                    </Text>
                  </FlexContainer>
                  <BackgroundLines className="flex items-center z-0">
                    <div className="flex w-full justify-end items-center gap-[-128px] relative">
                      <div className="rotate-[-10deg] z-0">
                        <NextImage
                          width={512}
                          height={768}
                          src="/media/static/coloringExamples/fairy_on_mushroom.png"
                          alt="girl on a mushroom"
                          className="rounded-xl ring-2 ring-black shadow-md hover:scale-125 hover:transition-all hover:duration-200"
                        />
                      </div>

                      <div className="z-10">
                        <NextImage
                          width={512}
                          height={768}
                          src="/media/static/coloringExamples/running_fox.png"
                          alt="fox running in the snow"
                          className="rounded-xl ring-2 ring-black shadow-lg hover:scale-125 hover:transition-all hover:duration-200"
                        />
                      </div>

                      <div className="rotate-10 z-0">
                        <NextImage
                          width={512}
                          height={768}
                          src="/media/static/coloringExamples/witch_girl_hut.png"
                          alt="witch girl in front of hut"
                          className="rounded-xl ring-2 ring-black shadow-md hover:scale-125 hover:transition-all hover:duration-200"
                        />
                      </div>
                    </div>
                  </BackgroundLines>
                </GridContainer>
              </div>
            </GridItem>
          </GridContainer>
        </GridItem>
        <div className="relative bg-black">
          <div className="absolute top-0 left-0 w-full overflow-hidden leading-[0]">
            <svg
              className="block w-full h-[100px]" // height can be adjusted
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 1440 320"
              preserveAspectRatio="none"
            >
              <path
                fill="#A2D2FF"
                fillOpacity="1"
                d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,117.3C672,139,768,213,864,224C960,235,1056,181,1152,170.7C1248,160,1344,192,1392,208L1440,224L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z"
              ></path>
            </svg>
          </div>

          {/* Content */}
          <div className="relative pt-[100px] pb-16 px-4">
            <Container className="relative">
              <FourClover
                className="absolute top-[-32px] left-[-96px] animate-spin"
                size={96}
                color="white"
              />
              <FlexContainer className="gap-2 mb-8" direction={FlexDirection.COL}>
                <Title className="text-white mt-8">Let us create the</Title>
                <Title className="text-5xl leading-[1.2] bg-linear-to-r from-purple-400 to-pink-600 bg-clip-text text-transparent">
                  perfect coloring page
                </Title>{' '}
                <Title level={HeaderLevel.H1} className="text-white">
                  for you.
                </Title>
              </FlexContainer>
            </Container>
            <InfiniteMovingImages
              className="w-full"
              speed="slow"
              items={[
                {
                  imageUrl: `/media/static/coloringExamples/cat_on_floor.png`,
                  caption: 'Cat resting on the floor of the living room!',
                },
                {
                  imageUrl: `/media/static/coloringExamples/cupcake_with_stars.png`,
                  caption: 'Cupcake adorned with stars.',
                },
                {
                  imageUrl: `/media/static/coloringExamples/fairy_on_mushroom.png`,
                  caption: 'Fairy sitting on a mushroom.',
                },
                {
                  imageUrl: `/media/static/coloringExamples/running_fox.png`,
                  caption: 'Front facing fox running through the snowy forest.',
                },
                {
                  imageUrl: `/media/static/coloringExamples/spongebob_hands_up.png`,
                  caption: 'Spongebob squarepants in the ocean!',
                },
                {
                  imageUrl: `/media/static/coloringExamples/witch_girl_hut.png`,
                  caption: 'A witch girl with fluttering butterflies.',
                },
              ]}
            />
          </div>
        </div>
        <GridItem colSpan={ColSpan.SPAN_1} className={'bg-white border-b-2 py-12 border-black'}>
          <Container>
            <GridContainer className={`w-full grid-cols-1 py-12 ${Spacing.ContentPadding} gap-8`}>
              <FlexContainer
                className="gap-4"
                direction={FlexDirection.COL}
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
              >
                <Text size="2xl" className="text-center">
                  Explore everything you can create with us.
                </Text>
                <FlexContainer
                  direction={FlexDirection.ROW}
                  align={AlignItems.CENTER}
                  className="gap-2 mb-2"
                >
                  <Title className="text-5xl text-center">
                    Take a look at all the features we offer!
                  </Title>
                </FlexContainer>
                <CircleArrowDown size={96} className="animate-bounce mt-10" />
              </FlexContainer>
            </GridContainer>
          </Container>
        </GridItem>
        <GridItem
          colSpan={ColSpan.SPAN_1}
          className={'bg-accent4-lighter border-b-2 py-12 border-black flex items-center'}
        >
          <div
            className={`w-full mx-auto sm:max-w-[99%] md:max-w-[90%] lg:max-w-[85%] xl:max-w-[80%] 2xl:max-w-[70%]`}
          >
            <GridContainer
              className={`w-full grid-cols-1 lg:grid-cols-2 ${Spacing.ContentPadding} gap-12`}
            >
              <FlexContainer
                className="gap-4 w-full"
                direction={FlexDirection.COL}
                justify={JustifyContent.CENTER}
                align={AlignItems.START}
              >
                <FlexContainer
                  direction={FlexDirection.ROW}
                  align={AlignItems.CENTER}
                  className="gap-2 mb-2"
                  wrap={false}
                >
                  <TextIcon size="32px" strokeWidth={2} />
                  <Title className="leading-none">Text to Coloring Page</Title>
                </FlexContainer>
                <Text variant="emphasis" size="xl">
                  {`Turn your 💡 ideas into custom coloring pages!`}
                </Text>
                <Text variant="description" size="xl">
                  {`Just describe what you want to see — our AI instantly transforms your words into beautifully detailed, printable coloring art.
                   Perfect for unique gifts, creative projects, or fun with kids.`}
                </Text>
              </FlexContainer>
              {/* <FlexContainer className="gap-[-64px] relative" direction={FlexDirection.ROW} justify={JustifyContent.CENTER} align={AlignItems.CENTER}> */}
              <div className="relative flex items-center px-4">
                {/* <div className="absolute inset-0">
                  <NextImage
                    width={1096}
                    height={580}
                    src="/media/static/crayonSmudge/yellow_crayon_smudge.svg"
                    alt=""
                    className=""
                  />
                </div> */}
                <div className="flex w-full justify-end items-center gap-[-64px] relative ">
                  <div className="rotate-[-10deg] z-0">
                    <NextImage
                      width={256}
                      height={384}
                      src="/media/static/coloringExamples/cat_on_floor.png"
                      alt="cat on the floor"
                      className="rounded-xl ring-2 ring-black shadow-md"
                    />
                  </div>
                  <div className="z-10">
                    <NextImage
                      width={256}
                      height={384}
                      src="/media/static/coloringExamples/cupcake_with_stars.png"
                      alt="cupcake with stars"
                      className="rounded-xl ring-2 ring-black shadow-lg"
                    />
                  </div>

                  <div className="rotate-10 z-0">
                    <NextImage
                      width={256}
                      height={384}
                      src="/media/static/coloringExamples/witch_girl_hut.png"
                      alt="witch girl in front of hut"
                      className="rounded-xl ring-2 ring-black shadow-md"
                    />
                  </div>
                </div>
              </div>
            </GridContainer>
          </div>
        </GridItem>
        <GridItem
          colSpan={ColSpan.SPAN_1}
          className={'bg-accent3-lighter border-b-2 border-black py-12'}
        >
          <div
            className={`w-full mx-auto sm:max-w-[99%] md:max-w-[90%] lg:max-w-[85%] xl:max-w-[80%] 2xl:max-w-[70%]`}
          >
            <GridContainer
              className={`w-full grid-cols-1 lg:grid-cols-2 ${Spacing.ContentPadding} gap-12`}
            >
              <div className="relative flex items-center justify-center order-2 lg:order-1">
                <ShowcaseCompare
                  firstImage="/media/static/coloringExamples/flamingo_normal.png"
                  secondImage="/media/static/coloringExamples/flamingo.png"
                />
              </div>
              <FlexContainer
                className="gap-4 order-1 lg:order-2"
                direction={FlexDirection.COL}
                justify={JustifyContent.CENTER}
                align={AlignItems.START}
              >
                <FlexContainer
                  direction={FlexDirection.ROW}
                  align={AlignItems.CENTER}
                  className="gap-2 mb-2 w-full"
                  wrap={false}
                >
                  <Wallpaper size={'32px'} strokeWidth={2} />
                  <Title>Image to Coloring Page</Title>
                </FlexContainer>
                <Text variant="emphasis" size="xl">
                  Turn any 🖼️ photo into a custom coloring page.
                </Text>
                <Text variant="description" size="xl">
                  {`Upload your favorite picture and let our AI transform it into a beautifully detailed coloring page. 
                  Great for kids, adults, gifts, and creative projects.`}
                </Text>
              </FlexContainer>
            </GridContainer>
          </div>
        </GridItem>
        <GridItem
          colSpan={ColSpan.SPAN_1}
          className={'bg-accent6-lighter border-b-2 py-12 border-black'}
        >
          <div
            className={`w-full mx-auto sm:max-w-[99%] md:max-w-[90%] lg:max-w-[85%] xl:max-w-[80%] 2xl:max-w-[70%]`}
          >
            <GridContainer
              className={`w-full grid-cols-1 lg:grid-cols-2 ${Spacing.ContentPadding} gap-12`}
            >
              <FlexContainer
                className="gap-4"
                direction={FlexDirection.COL}
                justify={JustifyContent.CENTER}
                align={AlignItems.START}
              >
                <FlexContainer
                  direction={FlexDirection.ROW}
                  align={AlignItems.CENTER}
                  className="gap-2 mb-2 w-full"
                  wrap={false}
                >
                  <Library size={'32px'} strokeWidth={2} />
                  <Title>Image Library</Title>
                </FlexContainer>
                <Text variant="emphasis" size="xl">
                  Explore our vast 📚 Image Library!
                </Text>
                <Text variant="description" size="xl">
                  {`Check out our robust image library, with over 100,000+ images to choose and create from! 
                  You'll never run out of material!`}
                </Text>
              </FlexContainer>
              <div className="relative flex items-center justify-center h-auto">
                <NextImage
                  className="object-cover"
                  width={1920}
                  height={1096}
                  src="/media/static/homePageImageElements/image_library_crayon.svg"
                  alt="image library demo"
                />
              </div>
            </GridContainer>
          </div>
        </GridItem>
        <GridItem
          colSpan={ColSpan.SPAN_1}
          className={'bg-accent2-lighter border-b-2 border-black py-12'}
        >
          <div
            className={`w-full mx-auto sm:max-w-[99%] md:max-w-[90%] lg:max-w-[85%] xl:max-w-[80%] 2xl:max-w-[70%]`}
          >
            <GridContainer
              className={`w-full grid-cols-1 lg:grid-cols-2 ${Spacing.ContentPadding} gap-8`}
            >
              <div className="relative flex items-center justify-center h-auto order-2 lg:order-1">
                <NextImage
                  className="object-cover max-w-[400px]"
                  width={1920}
                  height={1096}
                  src="/media/static/homePageImageElements/image_editor_background.svg"
                  alt="image editor demo"
                />
              </div>
              <FlexContainer
                className="gap-4 order-1 lg:order-2"
                direction={FlexDirection.COL}
                justify={JustifyContent.CENTER}
                align={AlignItems.START}
              >
                <FlexContainer
                  direction={FlexDirection.ROW}
                  align={AlignItems.CENTER}
                  className="gap-2 mb-2 w-full"
                  wrap={false}
                >
                  <Dna size={'32px'} strokeWidth={2} />
                  <Title>Image Editing Tools</Title>
                </FlexContainer>
                <Text variant="emphasis" size="xl">
                  ✏️ Edit your coloring page with ease!
                </Text>
                <Text variant="description" size="xl">
                  Need to have more control over your coloring page? We have robust editing tools
                  for you to ensure the coloring pages you created are as perfect as possible!
                </Text>
                <Text variant="emphasis" size="2xl">
                  What we offer:
                </Text>
                <ol className="space-y-2 [&>li]:list-none [&>li]:pl-4 [&>li]:before:content-['•'] [&>li]:before:relative [&>li]:before:left-0 [&>li]:before:mr-2">
                  <li>Contrast Normalizer</li>
                  <li>Background Remover</li>
                  <li>Upscaler</li>
                  <li>🌠 And more to come! 🌠</li>
                </ol>
              </FlexContainer>
            </GridContainer>
          </div>
        </GridItem>
        {/* Will be used in the future
        <GridItem colSpan={ColSpan.SPAN_1} className={'bg-main-lighter border-b-2 border-black'}>
          <Container>
            <GridContainer
              className={`w-full grid-cols-1 lg:grid-cols-2 py-8 ${Spacing.ContentPadding} gap-8`}
            >
              <FlexContainer
                className="gap-4"
                direction={FlexDirection.COL}
                justify={JustifyContent.CENTER}
                align={AlignItems.START}
              >
                <FlexContainer
                  direction={FlexDirection.ROW}
                  align={AlignItems.CENTER}
                  className="gap-2 mb-2 w-full"
                >
                  <Palette size={'32px'} strokeWidth={2} />
                  <Title>Online Coloring App</Title>
                  <Badge className="text-lg">Beta</Badge>
                </FlexContainer>
                <Text variant="description" size="xl">
                  {`Can't wait to have it printed? Immediately color your creations on our online coloring app!`}
                  <br />
                </Text>
                <Text variant="description" size="xl">
                  Currently still in beta, but do give it a try and let us know your thoughts!
                </Text>
              </FlexContainer>
              <div>PLACEHOLDER IMAGE</div>
            </GridContainer>
          </Container>
        </GridItem> */}
        <GridItem colSpan={ColSpan.SPAN_1} className={'bg-white border-b-2 border-black py-24'}>
          <Container>
            <FlexContainer
              direction={FlexDirection.ROW}
              justify={JustifyContent.CENTER}
              align={AlignItems.CENTER}
              wrap={false}
              className="w-full gap-3 mb-12 h-full"
            >
              <CircleHelp size={48} />
              <Title level={HeaderLevel.H2} className="text-4xl">
                How does ColorAria work?
              </Title>
            </FlexContainer>
            <GridContainer className="w-full grid-cols-1 md:grid-cols-3 gap-24">
              <FlexContainer
                className="w-full gap-2 h-full"
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
                direction={FlexDirection.COL}
              >
                <NextImage
                  src="/media/static/homePageImageElements/t2i_or_i2i_rep.svg"
                  alt="t2i or i2i"
                  width={560}
                  height={400}
                />
                <Title level={HeaderLevel.H4} className="text-center">
                  Step 1: Choose your generation method
                </Title>
                <Text className="text-center" variant="description">
                  {`Choose whether you wish to use your imagination in Text to Image, or use an existing
                  image in Image to Image.`}
                </Text>
              </FlexContainer>
              <FlexContainer
                className="w-full gap-2 h-full"
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
                direction={FlexDirection.COL}
              >
                <NextImage
                  src="/media/static/homePageImageElements/insert_text_or_image.svg"
                  alt="Insert text or image"
                  width={560}
                  height={400}
                />
                <Title level={HeaderLevel.H4} className="text-center">
                  Step 2: Generate your coloring page
                </Title>
                <Text className="text-center" variant="description">
                  {`Insert your text prompt or upload an image and generate your coloring page!`}
                </Text>
              </FlexContainer>
              <FlexContainer
                className="w-full gap-2 h-full"
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
                direction={FlexDirection.COL}
              >
                <NextImage
                  src="/media/static/homePageImageElements/download_coloring_image.svg"
                  alt="insert text or image"
                  width={560}
                  height={400}
                />
                <Title level={HeaderLevel.H4} className="text-center">
                  Step 3: Download your Image
                </Title>
                <Text className="text-center" variant="description">
                  {`Once satisfied, just hit that download button and print it out!`}
                </Text>
              </FlexContainer>
            </GridContainer>
          </Container>
        </GridItem>
        <section>
          <div className="h-full relative">
            <FlexContainer
              className="w-full px-16 py-24 gap-8 bg-black"
              direction={FlexDirection.COL}
              align={AlignItems.CENTER}
              justify={JustifyContent.CENTER}
            >
              <Text className="text-white" size="2xl">
                What are you waiting for?
              </Text>
              <Title
                level={HeaderLevel.H2}
                className="text-center text-4xl font-medium sm:text-5xl lg:text-7xl leading-tight! text-white"
              >
                Unleash your imagination! <br />
              </Title>
              <Title className="text-center text-4xl font-medium sm:text-5xl lg:text-7xl leading-tight! bg-linear-to-r from-purple-400 to-pink-600 bg-clip-text text-transparent">
                Color now!
              </Title>
              <Link href={`${HOST_URL}/register`} className="w-[80%] lg:w-[50%]">
                <Button
                  className="w-full shadow-[3px_3px_3px_rgba(254,110,148,1)]"
                  variant="secondary"
                  size="xl"
                >
                  <Text size="xl">Start now for free!</Text>
                </Button>
              </Link>
            </FlexContainer>
            <Sparkles className="bottom-5 right-5 z-0 absolute" color="white" size="5rem" />
            <Sparkles className="top-5 left-5 z-0 absolute" color="white" size="5rem" />
          </div>
        </section>
      </GridContainer>

      <div className="bg-main bottom-0 w-full">
        <MainFooter />
      </div>
    </div>
  )
}
