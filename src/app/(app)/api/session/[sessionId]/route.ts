import { StatusCodes } from 'http-status-codes'
import { HTTPException } from '@/app/(app)/_backend/common/exception'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import { stripe } from '@/lib/stripe'

export interface SessionInfo {
  paid: boolean
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ sessionId: string }> },
) {
  try {
    const { sessionId } = await params
    const session = await stripe.checkout.sessions.retrieve(sessionId)
    const clientReferenceId = session.client_reference_id
    if (!session.metadata) {
      throw new HTTPException('Empty session metadata', StatusCodes.BAD_REQUEST)
    }

    if (!clientReferenceId) {
      throw new HTTPException('Empty client reference id', StatusCodes.BAD_REQUEST)
    }

    if (session.payment_status === 'paid') {
      return new Response(
        JSON.stringify({
          message: 'User has successfully paid for the subscription',
          data: { paid: true },
        }),
      )
    } else if (session.payment_status === 'unpaid') {
      return new Response(JSON.stringify({ message: 'Unpaid', data: { paid: false } }))
    } else if (session.status === 'open' || session.status === 'expired') {
      return new Response(JSON.stringify({ message: 'Process interrupted', data: { paid: false } }))
    }
  } catch (error) {
    return errorHandler(error)
  }
}
