import { headers } from 'next/headers'
import { NextResponse } from 'next/server'
import { getPayload } from 'payload'
import PostHogClient from '@/lib/posthog'
import { stripe } from '@/lib/stripe'
import config from '@payload-config'
import SubscriptionPlanDAO from '../../_backend/common/dao/SubscriptionPlanDAO'
import SubscriptionRecordsDAO from '../../_backend/common/dao/SubscriptionRecordsDAO'
import { HTTPException } from '../../_backend/common/exception'
import { errorHandler } from '../../_backend/common/exception/errorHandler'
import SubscriptionService from '../../_backend/common/service/SubscriptionService'

interface StripeCheckoutSessionRequest {
  subscriptionName: string
  subscriptionDuration: 'month' | 'year'
  userId: string
}

export async function POST(request: Request) {
  try {
    const headersList = await headers()
    const origin =
      headersList.get('origin') || process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
    const { subscriptionName, subscriptionDuration, userId } =
      (await request.json()) as StripeCheckoutSessionRequest

    if (!subscriptionName || !subscriptionDuration || !userId) {
      throw new HTTPException('Invalid request, some fields are missing!', 400)
    }

    const payload = await getPayload({ config })

    const subscriptionPlanDAO = new SubscriptionPlanDAO(payload)
    const subscriptionRecordDAO = new SubscriptionRecordsDAO(payload)
    const subscriptionService = new SubscriptionService(subscriptionPlanDAO, subscriptionRecordDAO)

    const subscriptionPlan = await subscriptionService.retrieveSubscriptionByName(
      subscriptionName,
      subscriptionDuration,
    )

    const currentPeriodStart = Math.floor(Date.now() / 1000)

    const subscriptionRecord = await subscriptionService.addSubscription(
      null,
      userId,
      subscriptionPlan.id,
      userId,
      'incomplete',
      currentPeriodStart,
      currentPeriodStart + 60 * 60 * 24 * 30,
      '',
      null,
      null,
    )

    const subscriptionRecordId = subscriptionRecord.id

    const session = await stripe.checkout.sessions.create({
      line_items: [
        {
          price: subscriptionPlan.stripePriceId,
          quantity: 1,
        },
      ],
      metadata: {
        subscriptionRecordId,
        stripePriceId: subscriptionPlan.stripePriceId,
      },
      mode: 'subscription',
      client_reference_id: userId,
      success_url: `${origin}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${origin}/payment/cancel?session_id={CHECKOUT_SESSION_ID}`,
    })

    // Track checkout session created event
    try {
      const posthog = PostHogClient()
      posthog.capture({
        event: 'checkout_session_created',
        distinctId: userId,
        properties: {
          subscriptionName,
          subscriptionDuration,
          stripePriceId: subscriptionPlan.stripePriceId,
          sessionId: session.id,
        },
      })
    } catch (error) {
      console.error('PostHog tracking error:', error)
    }

    return NextResponse.json({ message: 'Checkout session created', data: { url: session.url } })
  } catch (err) {
    return errorHandler(err)
  }
}
