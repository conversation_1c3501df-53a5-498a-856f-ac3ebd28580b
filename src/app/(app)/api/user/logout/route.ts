import { StatusCodes } from 'http-status-codes'
import { cookies } from 'next/headers'
import { HTTPException } from '@/app/(app)/_backend/common/exception'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import { JWTType } from '@/app/(app)/_backend/common/utils/auth'
import PostHogClient from '@/lib/posthog'

export interface LogoutResponse {
  email: string
}

export async function POST(request: Request) {
  try {
    const cookieStore = cookies()
    const requestHeaders = new Headers(request.headers)
    const user = requestHeaders.get('x-user')

    // Track logout event before clearing cookies
    if (user) {
      try {
        const userObj = JSON.parse(user) as JWTType
        const posthog = PostHogClient()
        posthog.capture({
          event: 'user_logout',
          distinctId: userObj.email || userObj.id,
          properties: {
            userId: userObj.id,
          },
        })
      } catch (error) {
        console.error('PostHog tracking error:', error)
      }
    }

    await Promise.all([
      (await cookieStore).delete('la-token'),
      (await cookieStore).delete('auth-storage'),
    ])
    return new Response(
      JSON.stringify({
        message: 'User logged out!',
        data: {
          email: '',
        },
      }),
    )
  } catch (error) {
    return errorHandler(error)
  }
}
