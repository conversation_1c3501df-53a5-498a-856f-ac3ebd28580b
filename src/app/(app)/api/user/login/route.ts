import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import User<PERSON><PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { UserDTO, User } from '@/app/(app)/_backend/common/dto/user/UserDTO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'

export interface LoginRequest {
  email: string
  password: string
  recaptchaToken: string
}

export interface LoginResults {
  message: string
  data: User
}

export async function POST(request: Request) {
  try {
    const payload = await getPayload({ config })
    const { email, password, recaptchaToken } = (await request.json()) as LoginRequest
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)

    // test posthog-node usage
    const posthog = PostHogClient()
    posthog.capture({ event: 'password_login', distinctId: email })

    // check recaptcha token
    const requestBody = {
      event: {
        token: recaptchaToken,
        expectedAction: 'LOGIN',
        siteKey: process.env.NEXT_PUBLIC_RECAPTCHA_KEY_ID,
      },
    }
    const apiKey = process.env.RECAPTCHA_API_KEY

    const recaptchaResult = await fetch(
      `https://recaptchaenterprise.googleapis.com/v1/projects/coloraria/assessments?key=${apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Referer: process.env.NEXT_PUBLIC_HOST || 'localhost',
        },
        body: JSON.stringify(requestBody),
      },
    )
    const recaptchaData = await recaptchaResult.json()
    if (recaptchaData.tokenProperties.valid === false) {
      return new Response(JSON.stringify({ message: 'Invalid recaptcha token!' }), { status: 401 })
    }
    if (
      recaptchaData.tokenProperties.action === 'LOGIN' &&
      recaptchaData.tokenProperties.score < 0.5
    ) {
      return new Response(JSON.stringify({ message: 'Recaptcha score is too low!' }), {
        status: 401,
      })
    }

    const data = await userService.loginUser(email, password)
    const userDTO = new UserDTO(data.user)
    const modifiedUser = {
      ...data,
      user: userDTO.distil(),
    }
    const cookiesStore = await cookies()
    if (data?.token) {
      cookiesStore.set('la-token', data.token, {
        httpOnly: true,
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
      })
    }
    return new Response(JSON.stringify({ message: 'User is now logged in!', data: modifiedUser }))
  } catch (error) {
    return errorHandler(error)
  }
}
