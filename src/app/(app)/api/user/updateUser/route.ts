import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import User<PERSON><PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { User } from '@/app/(app)/_backend/common/dto/user/UserDTO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'

export interface UpdateUserRequest {
  firstName: string
  lastName: string
}

export interface UpdateResults {
  message: string
  data: User
}

export async function POST(request: Request) {
  try {
    const cookieStore = cookies()
    const authToken = (await cookieStore).get('la-token')?.value
    const { firstName, lastName } = (await request.json()) as UpdateUserRequest
    const payload = await getPayload({ config })
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)
    const verifyUser = await userService.verifyUser(authToken)
    const data = await userService.updateUser(verifyUser.id, firstName, lastName)

    // Track profile update event
    try {
      const posthog = PostHogClient()
      posthog.capture({
        event: 'profile_update',
        distinctId: verifyUser.email || verifyUser.id,
        properties: {
          userId: verifyUser.id,
          firstName,
          lastName,
        },
      })
    } catch (error) {
      console.error('PostHog tracking error:', error)
    }

    return new Response(JSON.stringify({ message: 'User updated!', data: data }))
  } catch (error) {
    return errorHandler(error)
  }
}
