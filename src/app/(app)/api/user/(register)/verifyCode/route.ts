import * as jose from 'jose'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { getCookieExpiration, getPayload } from 'payload'
import UserDAO from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import { ClientAuthState } from '@/app/(app)/_state/authState'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'

export interface VerifyCodeResponse {
  verified: boolean
  user: ClientAuthState
  expiry: number
}

export async function POST(request: Request) {
  try {
    const payload = await getPayload({ config })
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)

    const { userId, code } = await request.json()
    const verified = await userService.verifyOTPCode(userId, code)

    if (verified) {
      userDAO.setUserVerified(userId)
      const user = await userService.getUserById(userId)
      if (!user) {
        throw new Error('Internal server error')
      }
      const collectionConfig = payload.collections['public-users'].config
      const fieldsToSign = {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        freeCredits: user.freeCredits,
        paidCredits: user.paidCredits,
        rolloverCredits: user.rolloverCredits,
        role: user.role,
        collection: collectionConfig.slug,
      }
      // Encode the secret key
      const secret = new TextEncoder().encode(payload.secret)
      // Calculate expiration timestamp
      const expirationTime = Math.floor(Date.now() / 1000) + collectionConfig.auth.tokenExpiration
      // Sign the token using jose
      const token = await new jose.SignJWT(fieldsToSign)
        .setProtectedHeader({ alg: 'HS256' })
        .setIssuedAt()
        .setExpirationTime(expirationTime) // Use numeric timestamp
        .sign(secret)

      // const cookiesStore = await cookies()
      // cookiesStore.set('la-token', token, {
      //   httpOnly: true,
      //   sameSite: 'lax',
      //   secure: process.env.NODE_ENV === 'production',
      //   expires: getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration }),
      // })

      const expiresAt = getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration })
      const expTimestamp = Math.floor(expiresAt.getTime() / 1000)
      const authState = {
        state: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            freeCredits: user.freeCredits,
            paidCredits: user.paidCredits,
            rolloverCredits: user.rolloverCredits,
            role: user.role,
          },
          hasEverLoggedIn: true,
          isLoggedIn: true,
          isLoading: false,
          error: null,
          expiry: expTimestamp,
        },
        version: 0,
      }

      const response = new NextResponse(
        JSON.stringify({
          message: 'User verified!',
          data: { user: fieldsToSign, expiry: expTimestamp, verified: true },
        }),
      )
      response.cookies.set('la-token', token, {
        httpOnly: true,
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
        expires: getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration }),
      })
      response.cookies.set('auth-storage', JSON.stringify(authState), {
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
        expires: getCookieExpiration({ seconds: collectionConfig.auth.tokenExpiration }),
      })

      // Track email verification event
      try {
        const posthog = PostHogClient()
        posthog.capture({
          event: 'email_verification',
          distinctId: user.email,
          properties: {
            userId: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
          },
        })
      } catch (error) {
        console.error('PostHog tracking error:', error)
      }

      return response
    } else {
      return new Response(
        JSON.stringify({
          message: 'Invalid code!',
          data: { verified: false },
        }),
      )
    }
  } catch (error) {
    return errorHandler(error)
  }
}
