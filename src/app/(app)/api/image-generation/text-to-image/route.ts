import { getPayload } from 'payload'
import FeaturesDA<PERSON> from '@/app/(app)/_backend/common/dao/FeaturesDAO'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import User<PERSON>O from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import FeatureScopeService from '@/app/(app)/_backend/common/service/FeatureScopeService'
import ImageService, { T2IAspectRatio } from '@/app/(app)/_backend/common/service/ImageService'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import PostHogClient from '@/lib/posthog'
import { FeaturesType } from '@/utilities/local/enums'
import config from '@payload-config'

interface ImageGenerationRequestT2T {
  prompt: string
  aspectRatio: T2IAspectRatio
  numberOfImages: number
  highQuality: boolean
}

export interface ImageGenerationResponseT2I {
  remainingCredits: number
}

const mandatoryFeatures = [FeaturesType.TEXT_TO_COLOR, FeaturesType.TEXT_TO_COLOR_IMAGE_REPETITION]

const optionalFeatures = [FeaturesType.HIGH_NATIVE_QUALITY]

export async function POST(request: Request) {
  try {
    const { prompt, aspectRatio, numberOfImages, highQuality } =
      (await request.json()) as ImageGenerationRequestT2T
    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    const payload = await getPayload({ config })

    const userDAO = new UserDAO(payload)
    const featuresDAO = new FeaturesDAO(payload)
    const userService = new UserService(userDAO)
    const featureScopeService = new FeatureScopeService(userDAO, featuresDAO)

    const applicableFeatures = highQuality
      ? [...mandatoryFeatures, ...optionalFeatures]
      : mandatoryFeatures

    const costTabulation = await featureScopeService.costTabulation(user.id, applicableFeatures)
    const remainingCredits = (await userService.deductCredit(user.id, costTabulation)).freeCredits

    let quality = 'LOW_QUALITY' as 'LOW_QUALITY' | 'HIGH_QUALITY'
    if (highQuality) {
      quality = 'HIGH_QUALITY'
    }

    const generatedImageDAO = new GeneratedImageDAO(payload)

    const imageService = new ImageService(generatedImageDAO)
    imageService.textToImage(prompt, aspectRatio, quality, numberOfImages, user.id)

    // Track text-to-image generation event
    try {
      const posthog = PostHogClient()
      posthog.capture({
        event: 'text_to_image_generation',
        distinctId: user.id,
        properties: {
          prompt: prompt.substring(0, 100), // Truncate for privacy
          aspectRatio,
          quality,
          numberOfImages,
          remainingCredits,
        },
      })
    } catch (error) {
      console.error('PostHog tracking error:', error)
    }

    return new Response(
      JSON.stringify({
        message: 'Generating images...',
        data: {
          remainingCredits,
        },
      }),
      {
        status: 202,
      },
    )
  } catch (error) {
    return errorHandler(error)
  }
}
