import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import PaymentSuccessComponent from './component'

type Params = Promise<{ slug: string }>
type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>

// const getStripeSession = cache(async (sessionId: string) => {
//   const sessionData = await getStripeCheckoutSession(sessionId)
//   if (!sessionData) {
//     redirect('/')
//   }
//   return sessionData
// })

// export async function generateMetadata(props: {
//   params: Params
//   searchParams: SearchParams
// }): Promise<Metadata> {
//   const params = await props.searchParams
//   const sessionId = params.session_id as string
//   const sessionData = await getStripeSession(sessionId)

//   const paid = sessionData?.data.paid

//   return {
//     title: paid ? 'Payment successful | ColorAria' : 'Payment failed | ColorAria',
//     description: paid
//       ? 'Your payment was successful! Thank you for your support.'
//       : 'Unfortunately, your payment was not successful. Please try again.',
//   }
// }

export const metadata: Metadata = {
  title: 'Payment Gateway | ColorAria',
  description: 'Payment gateway for ColorAria',
}

export default async function PaymentSuccessPage(props: { searchParams: SearchParams }) {
  // Await the searchParams before accessing properties
  const params = await props.searchParams
  const sessionId = params.session_id as string

  console.log('Session ID:', sessionId)

  if (sessionId == null || sessionId.trim() === '') {
    console.log('Session ID is empty or null, redirecting to home page')
    redirect('/')
  }

  return <PaymentSuccessComponent sessionId={sessionId} />
}
