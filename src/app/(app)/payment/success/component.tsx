'use client'

import { useQuery } from '@tanstack/react-query'
import { CircleCheckBig, Images, Mail, XCircle } from 'lucide-react'
import NextImage from 'next/image'
import Link from 'next/link'
import { redirect } from 'next/navigation'
import { <PERSON><PERSON>oader } from 'react-spinners'
import { Button } from '../../_component/Button'
import Text from '../../_component/Text'
import Title from '../../_component/Title'
import Container from '../../_cssComp/Container'
import FlexContainer, {
  FlexDirection,
  JustifyContent,
  AlignItems,
} from '../../_cssComp/FlexContainer'
import GridContainer from '../../_cssComp/GridContainer'
import { getStripeCheckoutSession } from '../../_localApi/stripeClient'
import MainNavMenu from '../../_templates/MainNavMenu'

interface Props {
  sessionId: string
}

export default function PaymentSuccessComponent(props: Props) {
  const sessionId = props.sessionId

  const sessionDataUserQuery = useQuery({
    queryKey: ['stripeSession', sessionId],
    queryFn: async () => {
      const sessionData = await getStripeCheckoutSession(sessionId)
      console.log('Session Data:', sessionData)
      if (!sessionData) {
        return null
      }
      return sessionData
    },
    refetchOnWindowFocus: false,
    retry: false,
  })

  if (!sessionId) {
    redirect('/')
  }

  //   if (!sessionDataUserQuery.data) {
  //     redirect('/')
  //   }

  // To be decided, as this can mean many things.
  // Some API call failed or our failure. (Usually a failure on either Stripe's or our end)
  //   if (!sessionDataUserQuery.data.success) {
  //     redirect('/')
  //   }

  if (sessionDataUserQuery.isFetching) {
    return (
      <main>
        <FlexContainer direction={FlexDirection.COL} className="w-full">
          <div className="bg-main sticky top-0 w-full z-400">
            <MainNavMenu />
          </div>
          <section className="w-full">
            <Container>
              <FlexContainer
                direction={FlexDirection.COL}
                className="w-full h-[90vh]"
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
              >
                <MoonLoader />
              </FlexContainer>
            </Container>
          </section>
        </FlexContainer>
      </main>
    )
  }

  if (!sessionDataUserQuery.data) {
    return (
      <main>
        <FlexContainer direction={FlexDirection.COL} className="w-full">
          <div className="bg-main sticky top-0 w-full z-400">
            <MainNavMenu />
          </div>
          <section className="w-full">
            <Container>
              <FlexContainer
                direction={FlexDirection.COL}
                className="w-full h-[90vh]"
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
              >
                <NextImage
                  src="/media/static/illustration/purchaseCancelled.svg"
                  alt="purchase success"
                  width={270}
                  height={270}
                />
                <FlexContainer
                  direction={FlexDirection.ROW}
                  justify={JustifyContent.CENTER}
                  align={AlignItems.CENTER}
                  className="gap-4 mb-4"
                >
                  <CircleCheckBig size={32} />{' '}
                  <Title className="text-3xl">Something went wrong!</Title>
                </FlexContainer>
                <Text className="text-xl text-center">
                  {`Oops, looks like something went wrong here!`}
                </Text>
                <Text variant="description" className="text-xl text-center">
                  {`Let's head back and try again!`}
                </Text>
                <GridContainer className="grid-cols-1 md:grid-cols-2 mt-4 gap-4">
                  <Link href="/">
                    <Button variant="secondary" size="xl">
                      Return Home
                    </Button>
                  </Link>
                  <Link href="/dashboard">
                    <Button variant="emphasis" size="xl">
                      <Images /> Get started now!
                    </Button>
                  </Link>
                </GridContainer>
              </FlexContainer>
            </Container>
          </section>
        </FlexContainer>
      </main>
    )
  }

  //Successful Payment!
  if (sessionDataUserQuery.data.data.paid) {
    return (
      <main>
        <FlexContainer direction={FlexDirection.COL} className="w-full">
          <div className="bg-main sticky top-0 w-full z-400">
            <MainNavMenu />
          </div>
          <section className="w-full">
            <Container>
              <FlexContainer
                direction={FlexDirection.COL}
                className="w-full h-[90vh]"
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
              >
                <NextImage
                  src="/media/static/illustration/purchaseSuccess.svg"
                  alt="purchase success"
                  width={270}
                  height={270}
                />
                <FlexContainer
                  direction={FlexDirection.ROW}
                  justify={JustifyContent.CENTER}
                  align={AlignItems.CENTER}
                  className="gap-4 mb-4"
                >
                  <CircleCheckBig size={32} />{' '}
                  <Title className="text-3xl">Purchase Successful!</Title>
                </FlexContainer>
                <Text className="text-xl text-center">
                  {`Thank you very much for your purchase!`}
                </Text>
                <Text variant="description" className="text-xl text-center">
                  {`We're excited to have you onboard, let's not dally and start generating some coloring pages now!`}
                </Text>
                <Text variant="description" className="text-xl text-center">
                  Thank you once again!~
                </Text>
                <GridContainer className="grid-cols-1 md:grid-cols-2 mt-4 gap-4">
                  <Link href="/">
                    <Button variant="secondary" size="xl">
                      Return Home
                    </Button>
                  </Link>
                  <Link href="/dashboard/image-generation">
                    <Button variant="emphasis" size="xl">
                      <Images /> Get started now!
                    </Button>
                  </Link>
                </GridContainer>
              </FlexContainer>
            </Container>
          </section>
        </FlexContainer>
      </main>
    )
  }

  //Something went wrong while paying
  // (Strictly a payment intent was made, but something went wrong while paying - cards were rejected, etc.)
  if (!sessionDataUserQuery.data.data.paid) {
    return (
      <main>
        <FlexContainer direction={FlexDirection.COL} className="w-full">
          <div className="bg-main sticky top-0 w-full z-400">
            <MainNavMenu />
          </div>
          <section className="w-full">
            <Container>
              <FlexContainer
                direction={FlexDirection.COL}
                className="w-full h-[90vh]"
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
              >
                <NextImage
                  src="/media/static/illustration/purchaseCancelled.svg"
                  alt="purchase cancelled"
                  width={270}
                  height={270}
                />
                <FlexContainer
                  direction={FlexDirection.ROW}
                  justify={JustifyContent.CENTER}
                  align={AlignItems.CENTER}
                  className="gap-4 mb-4"
                >
                  <XCircle size={32} /> <Title className="text-3xl">Purchase failed</Title>
                </FlexContainer>
                <Text className="text-xl text-center">
                  {`Looks like something went wrong with our payment processor.`}
                </Text>
                <Text variant="description" className="text-xl text-center">
                  {`Please contact us with your email you used for payment and we will get back to you as
                  soon as possible!`}
                </Text>
                <Text variant="description" className="text-xl text-center">
                  Thank you!~
                </Text>
                <GridContainer className="grid-cols-1 md:grid-cols-2 mt-4 gap-4">
                  <Link href="/">
                    <Button variant="secondary" size="xl">
                      Return Home
                    </Button>
                  </Link>
                  {/* TODO: ADD A CONTACT BUTTON */}
                  <Button variant="emphasis" size="xl">
                    <Mail /> Contact Us
                  </Button>
                </GridContainer>
              </FlexContainer>
            </Container>
          </section>
        </FlexContainer>
      </main>
    )
  }
}
