'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { Thumbmark } from '@thumbmarkjs/thumbmarkjs'
import { Gift, Terminal } from 'lucide-react'
import NextImage from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { MoonLoader, PropagateLoader } from 'react-spinners'
import { z } from 'zod'
import { Alert, AlertDescription, AlertTitle } from '@/app/(app)/_component/Alert'
import { IconSize } from '@/utilities/local/css'
import { Button } from '../_component/Button'
import Divider from '../_component/Divider'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../_component/Form'
import { Input } from '../_component/Input'
import { ShineBorder } from '../_component/ShineBorder'
import Text from '../_component/Text'
import Title, { HeaderLevel } from '../_component/Title'
import Container from '../_cssComp/Container'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from '../_cssComp/FlexContainer'
import GridContainer, { GridItem, RowSpan, ColSpan } from '../_cssComp/GridContainer'
import { registerUsers } from '../_localApi/users'
import { ApiResponse } from '../_localApi/util'
import { RegisterResponse } from '../api/user/(register)/register/route'

const mainLink = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

interface RegisterProps {
  error?: string | string[]
  message?: string | string[]
}

function RegisterComponent(props: RegisterProps) {
  const { error, message } = props
  const router = useRouter()
  const [showAlert, setShowAlert] = useState(error ? true : false)
  const [bLoading, setBLoading] = useState(false)
  const [loadFingerprint, setLoadFingerprint] = useState(true)
  const [fingerprintHash, setFingerprintHash] = useState('')
  const [alertMessage, setAlertMessage] = useState({
    title: "Oops, we've faced an error!",
    message: message,
    variant: 'negative' as 'default' | 'positive' | 'negative' | 'destructive',
  })

  useEffect(() => {
    const tm = new Thumbmark()
    tm.get()
      .then((result) => {
        console.log(result.thumbmark)
        setFingerprintHash(result.thumbmark)
      })
      .finally(() => {
        setLoadFingerprint(false)
      })
  }, [])

  const formSchema = z
    .object({
      firstName: z.string().min(1, {
        message: 'First name is required!',
      }),
      lastName: z.string().min(1, {
        message: 'Last name is required!',
      }),
      email: z.string().email({
        message: 'This is not a valid email!',
      }),
      password: z.string().min(1, {
        message: 'Password is required!',
      }),
      repeat_password: z.string().min(1, {
        message: 'Repeat password is required!',
      }),
    })
    .refine((data) => data.password === data.repeat_password, {
      message: 'Password must match',
      path: ['repeat_password'],
    })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      repeat_password: '',
    },
  })

  const registerUserMutation = useMutation({
    mutationFn: (data: {
      firstName: string
      lastName: string
      email: string
      password: string
    }) => {
      return registerUsers(
        data.firstName,
        data.lastName,
        data.email,
        data.password,
        fingerprintHash,
      )
    },
    onSuccess: (data: ApiResponse<RegisterResponse>) => {
      console.log(data)
      setShowAlert(true)
      if (data.message.status === 200) {
        setAlertMessage({
          title: 'Success!',
          message: data.message.message,
          variant: 'positive',
        })
        // redirect to verification page
        router.push(`/verify?email=${data.data.email}&userId=${data.data.userId}`)
      } else if (data.message.status >= 400 && data.message.status < 500) {
        setAlertMessage({
          title: 'Something went wrong!',
          message: data.message.message,
          variant: 'negative',
        })
      } else {
        setAlertMessage({
          title: 'Something went wrong!',
          message: data.message.message,
          variant: 'negative',
        })
      }
    },
    onError: (error) => {
      setShowAlert(true)
      setAlertMessage({
        title: 'Something went wrong!',
        message: error.message,
        variant: 'negative',
      })
    },
    onMutate: () => {
      setBLoading(true)
    },
    onSettled: () => {
      setBLoading(false)
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    registerUserMutation.mutateAsync({
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.email,
      password: values.password,
    })
  }

  return (
    <FlexContainer
      className="bg-main border-2 border-black h-screen"
      direction={FlexDirection.COL}
      align={AlignItems.CENTER}
      justify={JustifyContent.CENTER}
    >
      <div className="relative w-[100%] sm:w-[95%] md:w-[70%] lg:w-[60%] xl:w-[50%] 2xl:w-[40%]">
        <FlexContainer className="mb-4 w-[100%] sm:w-[95%] md:w-[70%] lg:w-[60%] xl:w-[50%] 2xl:w-[40%]">
          <Link href={`${mainLink}`} className="text-black hover:text-gray-800 font-bold">
            ← Back to Home
          </Link>
        </FlexContainer>

        <FlexContainer
          className="z-10 relative border-black border-x-0 md:border-x-2 p-4 rounded-sm border-2 shadow-[3px_3px_0px_rgba(0,0,0,1)] bg-white w-full"
          direction={FlexDirection.COL}
        >
          <FlexContainer
            direction={FlexDirection.COL}
            className="gap-2 w-full"
            align={AlignItems.CENTER}
          >
            <Title level={HeaderLevel.H3}>𝄞 Sign up for ColorAria! 𝄞</Title>
            <Text>{`Let's get started by registering an account`}</Text>
          </FlexContainer>
          {showAlert && (
            <Alert className={'mt-4'} variant={alertMessage.variant}>
              <Terminal className="h-4 w-4" />
              <AlertTitle>{alertMessage.title}</AlertTitle>
              <AlertDescription>{alertMessage.message}</AlertDescription>
            </Alert>
          )}
          <Text className="my-4">
            {`Already have an account? `}
            <Link
              href={`${mainLink}/login`}
              className="text-accent2 hover:text-accent2-lighter underline"
            >
              Sign in!
            </Link>
          </Text>
          <Text variant="emphasis" className="mb-2">
            Sign up With:
          </Text>
          <FlexContainer
            direction={FlexDirection.COL}
            className="gap-2 w-full"
            align={AlignItems.CENTER}
          >
            {loadFingerprint ? (
              <PropagateLoader size={16} />
            ) : (
              <Button
                variant="secondary"
                className="w-full"
                onClick={() =>
                  (window.location.href = `/api/oauth2/authorize?fingerprintHash=${fingerprintHash}&redirectOnFail=/register`)
                }
              >
                <NextImage
                  className="w-6 h-6"
                  src="https://www.svgrepo.com/show/475656/google-color.svg"
                  width={24}
                  height={24}
                  loading="lazy"
                  alt="google logo"
                />
                <Text>Sign Up with Google</Text>
              </Button>
            )}
            <FlexContainer
              direction={FlexDirection.ROW}
              className="w-full gap-2"
              align={AlignItems.CENTER}
              justify={JustifyContent.CENTER}
            >
              <Divider className="w-[30%] md:w-[40%]" />
              <Text size="xs">Or continue with</Text>
              <Divider className="w-[30%] md:w-[40%]" />
            </FlexContainer>
            <Form {...form}>
              <FlexContainer className="space-y-4 font-bold w-full" align={AlignItems.CENTER}>
                <GridContainer className="grid-cols-1 md:grid-cols-2 w-full gap-2">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem className={'w-full'}>
                        <FormLabel>First name:</FormLabel>
                        <FormControl>
                          <Input className={'w-full'} placeholder="" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem className={'w-full'}>
                        <FormLabel>Last name:</FormLabel>
                        <FormControl>
                          <Input className={'w-full'} placeholder="" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </GridContainer>
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className={'w-full'}>
                      <FormLabel>Email:</FormLabel>
                      <FormControl>
                        <Input className={'w-full'} placeholder="" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem className={'w-full'}>
                      <FormLabel>Password:</FormLabel>
                      <FormControl>
                        <Input type="password" className={'w-full'} placeholder="" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="repeat_password"
                  render={({ field }) => (
                    <FormItem className={'w-full'}>
                      <FormLabel>Repeat Password:</FormLabel>
                      <FormControl>
                        <Input type="password" className={'w-full'} placeholder="" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </FlexContainer>
            </Form>
            {loadFingerprint ? (
              <PropagateLoader size={16} />
            ) : (
              <Button variant="emphasis" className="w-full" onClick={form.handleSubmit(onSubmit)}>
                {bLoading ? (
                  <MoonLoader size={IconSize.Small} />
                ) : (
                  <>
                    <Gift /> Register
                  </>
                )}
              </Button>
            )}
          </FlexContainer>
        </FlexContainer>
      </div>
    </FlexContainer>
  )
}

export default RegisterComponent
