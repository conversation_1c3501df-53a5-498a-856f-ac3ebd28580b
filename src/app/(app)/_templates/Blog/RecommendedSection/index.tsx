import { ClassValue } from 'clsx'
import Link from 'next/link'
import { createEmptyResponse } from 'node_modules/fabric/dist/src/parser/parseSVGDocument'
import { getPayload } from 'payload'
import PostDAO from '@/app/(app)/_backend/common/dao/PostDAO'
import PostService from '@/app/(app)/_backend/common/service/PostService'
import Title, { HeaderLevel } from '@/app/(app)/_component/Title'
import FlexContainer, { FlexDirection } from '@/app/(app)/_cssComp/FlexContainer'
import { isCategories, isCategory } from '@/collections/Categories'
import { isTags } from '@/collections/Tags'
import { cn } from '@/lib/utils'
import { Category, Post, Tag } from '@/payload-types'
import config from '@payload-config'
import BlogCard from '../BlogCard'
import BlogList from '../BlogList'

interface Props {
  tags?: (number | Tag)[] | null
  category: (number | Category) | null | undefined
  ignoreBlogId?: number
  className?: ClassValue
}

async function RecommendedSection(props: Props) {
  const { ignoreBlogId, tags, category, className } = props

  const BASE_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

  let tagArr = [] as number[]

  if (isTags(tags)) {
    tags.forEach((tag: Tag) => {
      tagArr.push(tag.id)
    })
  }

  const payload = await getPayload({ config })
  const postDAO = new PostDAO(payload)
  const postService = new PostService(postDAO)

  const recommendedPosts = await postService.getPostsDynamically({
    category: isCategory(category) ? category.id : category,
    tags: tagArr,
  })

  return (
    <FlexContainer direction={FlexDirection.COL} className={cn('gap-4', className)}>
      <Title level={HeaderLevel.H3} className={'mb-2'}>
        More articles to binge on:
      </Title>
      <FlexContainer direction={FlexDirection.COL} className="w-full">
        {recommendedPosts.docs.length > 1 ? (
          recommendedPosts.docs.map((post: Post) => {
            const categoryName = isCategory(post.category) ? post.category.name : post.category
            if (post.id !== ignoreBlogId) {
              return (
                <Link
                  className="w-full"
                  key={post.id}
                  href={`${BASE_URL}/blog/${categoryName}/${post.slug}`}
                >
                  <BlogList key={post.id} post={post} descriptionLimit={30} />
                </Link>
              )
            }
          })
        ) : (
          <div className="w-full">Nothing to see</div>
        )}
      </FlexContainer>
    </FlexContainer>
  )
}

export default RecommendedSection
