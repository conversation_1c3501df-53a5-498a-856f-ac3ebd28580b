'use client'
import { UseMutationResult } from '@tanstack/react-query'
import { Crown, Palette, Wallpaper } from 'lucide-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'

import { <PERSON><PERSON>oader } from 'react-spinners'
import { toast } from 'sonner'
import { v4 } from 'uuid'
import { Badge } from '@/app/(app)/_component/Badge'
import { Input } from '@/app/(app)/_component/Input'
import LockComponent from '@/app/(app)/_component/LockComponent'
import { Slider } from '@/app/(app)/_component/Slider'
import Text from '@/app/(app)/_component/Text'
import UploadBox from '@/app/(app)/_component/UploadBox'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import { ApiResponse } from '@/app/(app)/_localApi/util'
import { useAuthState } from '@/app/(app)/_state/authState'
import { useCreditStore } from '@/app/(app)/_state/creditStoreState'
import { ImageMetadata } from '@/app/(app)/_types/ImageMetadata'
import { ImageGenerationResponseI2I } from '@/app/(app)/api/image-generation/image-to-image/route'
import { Feature } from '@/payload-types'
import { ActionType, FeaturesType } from '@/utilities/local/enums'
import { Button } from '../../../_component/Button'
import Card from '../../../_component/Card/CardApplication'
import { Label } from '../../../_component/Label'
import { Textarea } from '../../../_component/Textarea'
import Title, { HeaderLevel } from '../../../_component/Title'

const mainLink = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

interface Props {
  generateImage: UseMutationResult<
    ApiResponse<ImageGenerationResponseI2I>,
    Error,
    {
      src: string
      numberOfImages: number
      aspectRatio: string
      unsplashSrc?: string
    },
    void
  >
  extImage: ImageMetadata | null
  userFeatures: string[]
  features: Feature[]
}

const CardHeader = () => {
  return (
    <div className="w-full h-full flex flex-row gap-2 items-center">
      <Wallpaper />
      <Title className="text-dark" level={HeaderLevel.H4}>
        Image to Coloring Image
      </Title>
    </div>
  )
}

function ImageToImage(props: Props) {
  const { generateImage, extImage, userFeatures, features } = props
  const [imageRepetition, setImageRepetition] = useState(1)
  const [disableGenerate, setDisableGenerate] = useState(true)
  const [aspectRatio, setAspectRatio] = useState('1:1')
  const [selectedImage, setSelectedImage] = useState<ImageMetadata>({
    id: v4(),
    src: extImage?.src || '',
    width: 50,
    height: 50,
    actionType: ActionType.IMAGE_TO_COLOR,
  })

  const maxImageReptition = 3
  const authUser = useAuthState()
  const creditStore = useCreditStore()

  const imageRepetitionCreditCost = features.filter(
    (f) => f.feature === FeaturesType.IMAGE_TO_COLOR_IMAGE_REPETITION,
  )[0].creditCost

  useEffect(() => {
    if (extImage) {
      setSelectedImage({
        id: v4(),
        src: extImage.src,
        width: extImage.width,
        height: extImage.height,
        actionType: ActionType.IMAGE_TO_COLOR,
        unsplashSrc: extImage.unsplashSrc,
      })
    }
  }, [extImage])

  const onGenerateImage = async () => {
    if (imageRepetition > maxImageReptition || imageRepetition < 1 || selectedImage.src === '') {
      toast.error(
        'Image cannot be empty or Image Repetition must be between 1 and ' + maxImageReptition,
        {
          position: 'top-right',
        },
      )
      setImageRepetition(1)
      return
    }
    await generateImage.mutateAsync({
      src: selectedImage.src,
      numberOfImages: imageRepetition,
      aspectRatio: aspectRatio,
      unsplashSrc: extImage?.unsplashSrc,
    })
  }

  useEffect(() => {
    if (imageRepetition <= maxImageReptition && imageRepetition > 0 && selectedImage.src !== '') {
      setDisableGenerate(false)
    } else {
      setDisableGenerate(true)
    }
  }, [imageRepetition, selectedImage])

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()

      reader.onload = () => {
        if (reader.result) {
          const img = new Image()
          img.onload = () => {
            setSelectedImage({
              id: v4(),
              src: reader.result as string,
              width: img.width,
              height: img.height,
              actionType: ActionType.IMAGE_TO_COLOR,
              unsplashSrc: '',
            })
          }
          img.src = reader.result as string
        }
      }

      reader.readAsDataURL(file)
    }
  }

  return (
    <Card title={<CardHeader />}>
      <div className="w-full grid grid-cols-12">
        <div className="flex flex-col w-full col-span-12 gap-3">
          <div className="flex items-center justify-start gap-3 flex-row sm:flex-col sm:items-start w-full">
            <FlexContainer
              direction={FlexDirection.ROW}
              justify={JustifyContent.CENTER}
              align={AlignItems.CENTER}
              className="gap-2"
            >
              <Text variant="emphasis">Upload an Image:</Text>
              <Label>
                <Link
                  className="text-accent5 hover:text-accent5-lighter underline"
                  href={`${mainLink}/dashboard/image-library`}
                >
                  (Need inspiration?)
                </Link>
              </Label>
            </FlexContainer>
            <FlexContainer direction={FlexDirection.ROW} className="w-full">
              <UploadBox
                existingImage={selectedImage.src}
                description="Landscapes, animals, nature, etc are supported"
                handleChange={handleImageChange}
              />
            </FlexContainer>
          </div>
          <LockComponent
            feature={FeaturesType.TEXT_TO_COLOR_IMAGE_REPETITION}
            userFeatures={userFeatures}
            creditCost={imageRepetitionCreditCost}
            mode="disable"
            costMode="onMount"
            className="flex flex-row gap-4 w-full col-span-12 items-center"
            multiplier={imageRepetition}
          >
            <FlexContainer direction={FlexDirection.COL} className="gap-4 w-full mt-2">
              <FlexContainer
                direction={FlexDirection.ROW}
                className="gap-2 w-full"
                align={AlignItems.CENTER}
              >
                <Label>
                  <Text variant="emphasis">Image Repetition:</Text>
                </Label>
                {authUser.user?.role.toLowerCase() === 'free' ? (
                  <Badge variant="destructive">
                    <Crown />
                    Premium Only
                  </Badge>
                ) : (
                  <></>
                )}
              </FlexContainer>
              <Slider
                value={[imageRepetition]}
                max={3}
                min={1}
                step={1}
                onValueChange={(e) => setImageRepetition(e[0])}
              />
              <Input
                type="number"
                max={3}
                min={1}
                value={imageRepetition}
                onChange={(e) =>
                  setImageRepetition(e.target.value !== '' ? parseInt(e.target.value) : 0)
                }
              />
              <Text size="sm" variant="description">
                {`We cannot guarantee the first generated image is perfect, therefore it's recommended
                to generate more than 1.`}
              </Text>
            </FlexContainer>
          </LockComponent>
        </div>
        <Button
          className="mt-5 w-full col-span-12"
          variant="emphasis"
          disabled={disableGenerate}
          onClick={onGenerateImage}
        >
          {generateImage.isPending ? (
            <MoonLoader color="white" size={20} />
          ) : (
            <>
              <Palette />
              Generate Image (-{creditStore.getTotal()})
            </>
          )}
        </Button>
      </div>
    </Card>
  )
}

export default ImageToImage
