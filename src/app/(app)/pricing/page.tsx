import { BadgePlus, GemIcon, <PERSON>rk<PERSON> } from 'lucide-react'
import type { Metadata } from 'next'
import NextImage from 'next/image'
import Link from 'next/link'
import { SubscriptionMap } from './subscriptionMap'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../_component/Accordion'
import { Badge } from '../_component/Badge'
import { Button } from '../_component/Button'
import SubscriptionButton from '../_component/Button/variants/SubscriptionButton'
import CardPrice from '../_component/Card/CardPrice'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../_component/Tabs'
import Text from '../_component/Text'
import Title, { HeaderLevel } from '../_component/Title'
import Container from '../_cssComp/Container'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from '../_cssComp/FlexContainer'
import GridContainer, { GridItem } from '../_cssComp/GridContainer'
import MainFooter from '../_templates/MainFooter'
import MainNavMenu from '../_templates/MainNavMenu'

//NOTE THAT THE FAQ iS TENTATIVE, PLEASE CHECK AND REVISE

export const metadata: Metadata = {
  title: 'Pricing Page | ColorAria',
  description: 'The pricing page for ColorAria services.',
}

function PricingPage() {
  const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
  return (
    <main>
      <div className={`flex flex-col w-full`}>
        <div className="bg-main sticky top-0 w-full z-400">
          <MainNavMenu />
        </div>
        <section>
          <GridContainer
            className={`w-full grid-cols-1 bg-accent4-lighter border-b-2 border-black`}
          >
            <Container className={`w-full`}>
              <FlexContainer
                direction={FlexDirection.COL}
                className="w-full pt-8 gap-2"
                align={AlignItems.CENTER}
                justify={JustifyContent.CENTER}
              >
                <FlexContainer
                  direction={FlexDirection.ROW}
                  align={AlignItems.CENTER}
                  justify={JustifyContent.CENTER}
                  className="gap-2"
                >
                  <GemIcon color="black" strokeWidth={2} size={32} />
                  <Title level={HeaderLevel.H1}>Pricing Plan</Title>
                </FlexContainer>
                <Text className="text-center w-[70%] font-light" size="lg">
                  Simple, and transparent pricing! <br />
                  We hope you enjoy our services, and color away!~
                </Text>
              </FlexContainer>
              <Tabs defaultValue="monthly">
                <TabsList className="grid w-full grid-cols-2 mt-4">
                  <TabsTrigger value="monthly">Monthly</TabsTrigger>
                  <TabsTrigger value="yearly">
                    Yearly <Badge className="ml-2">30% off!</Badge>
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="monthly">
                  <GridContainer
                    className={`w-full gap-4 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mt-8`}
                    columns={1}
                  >
                    <CardPrice
                      title={SubscriptionMap['PRELUDE_PLAN'].monthly.title}
                      subtitle={SubscriptionMap['PRELUDE_PLAN'].subtitle}
                      description={SubscriptionMap['PRELUDE_PLAN'].description}
                      price={SubscriptionMap['PRELUDE_PLAN'].monthly.price}
                      className={
                        'bg-white border-2 rounded-sm shadow-[3px_3px_0px_rgba(254,110,148,1)]'
                      }
                    >
                      <FlexContainer direction={FlexDirection.COL} className="gap-8 w-full">
                        <FlexContainer direction={FlexDirection.COL} className="gap-2 w-full">
                          <Title level={HeaderLevel.H4} className="mb-2">
                            Features:
                          </Title>
                          <ul className="flex gap-2 flex-col">
                            {SubscriptionMap['PRELUDE_PLAN'].features.map((feature) => {
                              if (feature.available) {
                                return <li key={feature.description}>{feature.description}</li>
                              } else {
                                return (
                                  <li
                                    key={feature.description}
                                    className="text-gray-500 line-through"
                                  >
                                    {feature.description}
                                  </li>
                                )
                              }
                            })}
                          </ul>
                          <Text variant="description" size="sm" className="italic mt-2">
                            More features coming soon~
                          </Text>
                        </FlexContainer>
                        <SubscriptionButton
                          subscriptionName={SubscriptionMap['PRELUDE_PLAN'].monthly.title}
                          subscriptionDuration="month"
                        />
                      </FlexContainer>
                    </CardPrice>
                    <CardPrice
                      title={SubscriptionMap['MUSE_PLAN'].monthly.title}
                      subtitle={SubscriptionMap['MUSE_PLAN'].subtitle}
                      description={SubscriptionMap['MUSE_PLAN'].description}
                      price={SubscriptionMap['MUSE_PLAN'].monthly.price}
                      highlight
                      star
                      className={
                        'bg-white border-2 rounded-sm shadow-[3px_3px_0px_rgba(254,110,148,1)]'
                      }
                    >
                      <FlexContainer direction={FlexDirection.COL} className="gap-8 w-full">
                        <FlexContainer direction={FlexDirection.COL} className="gap-2" wrap>
                          <Title level={HeaderLevel.H4} className="mb-2">
                            Features:
                          </Title>
                          <ul className="flex gap-2 flex-col">
                            {SubscriptionMap['MUSE_PLAN'].features.map((feature) => {
                              if (feature.available) {
                                return <li key={feature.description}>{feature.description}</li>
                              } else {
                                return (
                                  <li
                                    key={feature.description}
                                    className="text-gray-500 line-through"
                                  >
                                    {feature.description}
                                  </li>
                                )
                              }
                            })}
                          </ul>
                          <Text variant="description" size="sm" className="italic mt-2">
                            More features coming soon~
                          </Text>
                        </FlexContainer>
                        <Button variant="emphasis" size="xl" className="w-full">
                          <BadgePlus /> Subscribe!
                        </Button>
                      </FlexContainer>
                    </CardPrice>
                    <CardPrice
                      title={SubscriptionMap['ARIA_PLAN'].monthly.title}
                      subtitle={SubscriptionMap['ARIA_PLAN'].subtitle}
                      description={SubscriptionMap['ARIA_PLAN'].description}
                      price={SubscriptionMap['ARIA_PLAN'].monthly.price}
                      className={
                        'bg-white border-2 rounded-sm shadow-[3px_3px_0px_rgba(254,110,148,1)]'
                      }
                    >
                      <FlexContainer direction={FlexDirection.COL} className="gap-8 w-full">
                        <FlexContainer direction={FlexDirection.COL} className="gap-2 w-full">
                          <Title level={HeaderLevel.H4} className="mb-2">
                            Features:
                          </Title>
                          <ul className="flex gap-2 flex-col">
                            {SubscriptionMap['ARIA_PLAN'].features.map((feature) => {
                              if (feature.available) {
                                return <li key={feature.description}>{feature.description}</li>
                              } else {
                                return (
                                  <li
                                    key={feature.description}
                                    className="text-gray-500 line-through"
                                  >
                                    {feature.description}
                                  </li>
                                )
                              }
                            })}
                          </ul>
                          <Text variant="description" size="sm" className="italic mt-2">
                            More features coming soon~
                          </Text>
                        </FlexContainer>
                        <FlexContainer className="w-full">
                          <Button variant="emphasis" size="xl" className="w-full">
                            <BadgePlus /> Subscribe!
                          </Button>
                        </FlexContainer>
                      </FlexContainer>
                    </CardPrice>
                  </GridContainer>
                </TabsContent>
                <TabsContent value="yearly">
                  <GridContainer
                    className={`w-full gap-4 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mt-8`}
                    columns={1}
                  >
                    <CardPrice
                      title={SubscriptionMap['PRELUDE_PLAN'].monthly.title}
                      subtitle={SubscriptionMap['PRELUDE_PLAN'].subtitle}
                      description={SubscriptionMap['PRELUDE_PLAN'].description}
                      price={SubscriptionMap['PRELUDE_PLAN'].yearly.price}
                      className={
                        'bg-white border-2 rounded-sm shadow-[3px_3px_0px_rgba(254,110,148,1)]'
                      }
                    >
                      <FlexContainer direction={FlexDirection.COL} className="gap-8 w-full">
                        <FlexContainer direction={FlexDirection.COL} className="gap-2">
                          <Title level={HeaderLevel.H4} className="mb-2">
                            Features:
                          </Title>
                          <ul className="flex gap-2 flex-col">
                            {SubscriptionMap['PRELUDE_PLAN'].features.map((feature) => {
                              if (feature.available) {
                                return <li key={feature.description}>{feature.description}</li>
                              } else {
                                return (
                                  <li
                                    key={feature.description}
                                    className="text-gray-500 line-through"
                                  >
                                    {feature.description}
                                  </li>
                                )
                              }
                            })}
                          </ul>
                          <Text variant="description" size="sm" className="italic mt-2">
                            More features coming soon~
                          </Text>
                        </FlexContainer>
                        <SubscriptionButton
                          subscriptionName={SubscriptionMap['PRELUDE_PLAN'].yearly.title}
                          subscriptionDuration="year"
                        />
                      </FlexContainer>
                    </CardPrice>
                    <CardPrice
                      title={SubscriptionMap['MUSE_PLAN'].monthly.title}
                      subtitle={SubscriptionMap['MUSE_PLAN'].subtitle}
                      description={SubscriptionMap['MUSE_PLAN'].description}
                      price={SubscriptionMap['MUSE_PLAN'].yearly.price}
                      highlight
                      star
                      className={
                        'bg-white border-2 rounded-sm shadow-[3px_3px_0px_rgba(254,110,148,1)]'
                      }
                    >
                      <FlexContainer direction={FlexDirection.COL} className="gap-8 w-full">
                        <FlexContainer direction={FlexDirection.COL} className="gap-2">
                          <Title level={HeaderLevel.H4} className="mb-2">
                            Features:
                          </Title>
                          <ul className="flex gap-2 flex-col">
                            {SubscriptionMap['MUSE_PLAN'].features.map((feature) => {
                              if (feature.available) {
                                return <li key={feature.description}>{feature.description}</li>
                              } else {
                                return (
                                  <li
                                    key={feature.description}
                                    className="text-gray-500 line-through"
                                  >
                                    {feature.description}
                                  </li>
                                )
                              }
                            })}
                          </ul>
                          <Text variant="description" size="sm" className="italic mt-2">
                            More features coming soon~
                          </Text>
                        </FlexContainer>
                        <Button variant="emphasis" size="xl" className="w-full">
                          <BadgePlus /> Subscribe!
                        </Button>
                      </FlexContainer>
                    </CardPrice>
                    <CardPrice
                      title={SubscriptionMap['ARIA_PLAN'].monthly.title}
                      subtitle={SubscriptionMap['ARIA_PLAN'].subtitle}
                      price={SubscriptionMap['ARIA_PLAN'].yearly.price}
                      description={SubscriptionMap['ARIA_PLAN'].description}
                      className={
                        'bg-white border-2 rounded-sm shadow-[3px_3px_0px_rgba(254,110,148,1)]'
                      }
                    >
                      <FlexContainer direction={FlexDirection.COL} className="gap-8 w-full">
                        <FlexContainer direction={FlexDirection.COL} className="gap-2 w-full">
                          <Title level={HeaderLevel.H4} className="mb-2">
                            Features:
                          </Title>
                          <ul className="flex gap-2 flex-col">
                            {SubscriptionMap['ARIA_PLAN'].features.map((feature) => {
                              if (feature.available) {
                                return <li key={feature.description}>{feature.description}</li>
                              } else {
                                return (
                                  <li
                                    key={feature.description}
                                    className="text-gray-500 line-through"
                                  >
                                    {feature.description}
                                  </li>
                                )
                              }
                            })}
                          </ul>
                          <Text variant="description" size="sm" className="italic mt-2">
                            More features coming soon~
                          </Text>
                        </FlexContainer>
                        <FlexContainer className="w-full">
                          <Button variant="emphasis" size="xl" className="w-full">
                            <BadgePlus /> Subscribe!
                          </Button>
                        </FlexContainer>
                      </FlexContainer>
                    </CardPrice>
                  </GridContainer>
                </TabsContent>
              </Tabs>
            </Container>
          </GridContainer>
        </section>
        <section>
          <div className="bg-accent1 border-b-2 border-black relative mx-auto">
            <Container className="relative mx-auto 2xl:w-[40%]">
              <GridContainer className="grid-cols-4 w-full gap-4 relative">
                <GridItem className="col-span-4 justify-self-center self-center w-full">
                  <FlexContainer
                    direction={FlexDirection.COL}
                    className="py-8 relative z-5 w-full gap-4"
                    align={AlignItems.CENTER}
                    justify={JustifyContent.CENTER}
                  >
                    <FlexContainer
                      className="w-full gap-4 relative"
                      direction={FlexDirection.ROW}
                      justify={JustifyContent.CENTER}
                      align={AlignItems.CENTER}
                    >
                      <NextImage
                        src="/media/static/illustration/icon/faqBox.svg" //from canva
                        alt=""
                        style={{
                          width: '10%',
                          height: 'auto',
                        }}
                        width={200}
                        height={200}
                        draggable={false}
                      />
                      <FlexContainer
                        direction={FlexDirection.COL}
                        className="mb-4 gap-2"
                        align={AlignItems.CENTER}
                        justify={JustifyContent.CENTER}
                      >
                        <Title level={HeaderLevel.H2} className="text-center">
                          Frequently Asked Question:
                        </Title>
                        <Text className="text-center" size="lg">
                          Check out some commonly asked questions here!
                        </Text>
                      </FlexContainer>
                    </FlexContainer>
                    <FlexContainer
                      className="w-full gap-2"
                      direction={FlexDirection.COL}
                      align={AlignItems.CENTER}
                      justify={JustifyContent.CENTER}
                    >
                      <Accordion
                        className="col-span-12 w-full"
                        // className="col-span-12 w-[80%] md:w-[70%] lg:w-[60%] xl:w-[50%]
                        type="single"
                        collapsible
                      >
                        <AccordionItem className="max-w-full" value="item-1">
                          <AccordionTrigger>
                            Does ColorAria have a free tier I can try out?
                          </AccordionTrigger>
                          <AccordionContent className="flex flex-col gap-4">
                            <Text>{`Yes, we have a free tier!`}</Text>
                            <Text>
                              Just{' '}
                              <Link
                                href={'/register'}
                                className="text-accent2 hover:text-accent2-lighter underline"
                              >
                                sign up
                              </Link>{' '}
                              to get started with 4 credits!
                            </Text>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                      <Accordion
                        // className="col-span-12 w-[80%] md:w-[70%] lg:w-[60%] xl:w-[50%]"
                        className="col-span-12 w-full"
                        type="single"
                        collapsible
                      >
                        <AccordionItem className="max-w-full" value="item-1">
                          <AccordionTrigger>{`What is 'Higher Native Print Quality'?`}</AccordionTrigger>
                          <AccordionContent className="flex flex-col gap-4">
                            <Text>
                              {`When you become a Muse member, you'll have the option generate all images at a higher
                      native quality. For example, with a 3:2 aspect ratio, the standard quality is
                      768 x 512 pixels. While the high-quality print is 1536 x 1024 pixels.`}
                            </Text>
                            <Text>
                              {`Base quality is 512 x 512 pixels, and high quality is 1024 x 1024 pixels.`}
                            </Text>
                            <Text>
                              {`Note that you can still have access to our upscaler to upscale your images
                      by 2x!`}
                            </Text>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                      <Accordion className="col-span-12 w-full" type="single" collapsible>
                        <AccordionItem className="max-w-full" value="item-1">
                          <AccordionTrigger>{`Can I try out coloring on ColorAria?`}</AccordionTrigger>
                          <AccordionContent className="flex flex-col gap-4">
                            {/* <Text>
                              {`Yeap, you can! Head over to our `}
                              <Link
                                href="/coloring"
                                target="_blank"
                                className="text-accent2 hover:text-accent2-lighter underline"
                              >
                                Online Coloring App
                              </Link>
                              {` and try it out now!`}
                            </Text> */}
                            <Text>
                              {
                                'Currently this is still a work in progress, but we are working hard on it! Stay tuned for updates!'
                              }
                            </Text>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                      <Accordion className="col-span-12 w-full" type="single" collapsible>
                        <AccordionItem className="max-w-full" value="item-1">
                          <AccordionTrigger>{`Are there higher upscale tiers?`}</AccordionTrigger>
                          <AccordionContent className="flex flex-col gap-4">
                            <Text>
                              {`Currently we only support up to 2x upscaler using algorithmic upscaling! 
                              Our algorithmic upscaler ensures that the image is upscaled without losing too much quality, 
                              and is the most efficient way to upscale images without altering the base image.`}
                            </Text>
                            <Text>
                              {`But we are working hard on bringing you a more powerful AI-based upscaler that can go up to 4x!`}
                            </Text>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                      <Accordion
                        // className="col-span-12 w-[80%] md:w-[70%] lg:w-[60%] xl:w-[50%]"
                        className="col-span-12 w-full"
                        type="single"
                        collapsible
                      >
                        <AccordionItem className="max-w-full" value="item-1">
                          <AccordionTrigger>
                            {`What if the image doesn't turn out the way I want?`}
                          </AccordionTrigger>
                          <AccordionContent className="flex flex-col gap-4">
                            <Text>
                              {`That's to be expected. This is the AI after all. It would be boring if it's all the same, right?`}
                            </Text>
                            <Text>
                              <i>{`"But I had something in mind, and the AI just didn't quite get it!"`}</i>
                              <br /> {`Don't worry, we have you covered!`}
                            </Text>
                            <Text>
                              {`Try out our guided prompt feature! The prompts are specifically tuned with the AI,
                      allowing you to achieve your desired coloring page.`}
                            </Text>
                            <Text>{`Please note that there is no refund on credits used as image generation is a computationally expensive operation.`}</Text>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    </FlexContainer>
                  </FlexContainer>
                </GridItem>
              </GridContainer>
            </Container>
          </div>
        </section>
        <section>
          <div className="h-full relative">
            <FlexContainer
              className="w-full px-16 py-24 gap-8 bg-black"
              direction={FlexDirection.COL}
              align={AlignItems.CENTER}
              justify={JustifyContent.CENTER}
            >
              <Text className="text-white" size="2xl">
                What are you waiting for?
              </Text>
              <Title
                level={HeaderLevel.H2}
                className="text-center text-4xl font-medium sm:text-5xl lg:text-7xl leading-tight! text-white"
              >
                Unleash your imagination! <br />
              </Title>
              <Title className="text-center text-4xl font-medium sm:text-5xl lg:text-7xl leading-tight! bg-linear-to-r from-purple-400 to-pink-600 bg-clip-text text-transparent">
                Color now!
              </Title>
              <Link href={`${HOST_URL}/register`} className="w-[50%]">
                <Button
                  className="w-full shadow-[3px_3px_3px_rgba(254,110,148,1)]"
                  variant="secondary"
                  size="xl"
                >
                  <Text size="xl">Start now for free!</Text>
                </Button>
              </Link>
            </FlexContainer>
            <Sparkles className="bottom-5 right-5 z-0 absolute" color="white" size="5rem" />
            <Sparkles className="top-5 left-5 z-0 absolute" color="white" size="5rem" />
          </div>
        </section>
        <div className="bg-main w-full">
          <MainFooter />
        </div>
      </div>
    </main>
  )
}

export default PricingPage
