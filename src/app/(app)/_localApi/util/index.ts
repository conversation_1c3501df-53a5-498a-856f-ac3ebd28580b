import { StatusCodes } from 'http-status-codes'

export interface ApiResponse<T> {
  success: boolean
  data: T
  message: {
    status: number
    message: string
  }
}

//TODO: Should make those throw errors so they work well with error boundaries in React Query
export async function apiFetch<T>(
  url: string,
  options?: RequestInit,
  message: string = 'An error occurred',
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url, options)
    console.log(response)
    const result = await response.json()

    if (!response.ok) {
      return {
        success: false,
        data: null as unknown as T,
        message: {
          status: response.status,
          message: result.message || message,
        },
      }
    }

    return {
      success: true,
      data: result.data,
      message: {
        status: response.status,
        message: result.message || message,
      },
    }
  } catch (err) {
    return {
      success: false,
      data: null as unknown as T, // Provide a default value for result
      message: {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: (err as Error).message || message,
      },
    }
  }
}
