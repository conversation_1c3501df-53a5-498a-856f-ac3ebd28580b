import { FieldHook } from 'payload'

const format = (val: string): string =>
  val
    .replace(/ /g, '-')
    .replace(/[^\w-/]+/g, '')
    .toLowerCase()

const formatSlug =
  (fallbackTitle: string): FieldHook =>
  async ({ value, originalDoc, data, req }) => {
    if (typeof value === 'string') {
      return format(value)
    }
    const fallbackDataTitle = data?.[fallbackTitle] || originalDoc?.[fallbackTitle]

    if (fallbackDataTitle && typeof fallbackDataTitle === 'string') {
      return format(fallbackDataTitle)
    }

    return value
  }

// const formatSlug =
//   (fallbackTitle: string, fallbackCategory: string): FieldHook =>
//   async ({ value, originalDoc, data, req }) => {
//     if (typeof value === 'string') {
//       return format(value)
//     }
//     const fallbackDataTitle = data?.[fallbackTitle] || originalDoc?.[fallbackTitle]
//     const fallbackCategoryId = data?.[fallbackCategory] || originalDoc?.[fallbackCategory]

//     let categoryName = ''
//     if (fallbackCategoryId && req?.payload) {
//       try {
//         const category = await req.payload.findByID({
//           collection: 'categories',
//           id: fallbackCategoryId,
//         })
//         categoryName = category?.name || ''
//       } catch (error) {
//         console.log(error)
//       }
//     }

//     if (fallbackDataTitle && typeof fallbackDataTitle === 'string') {
//       const slugParts = [fallbackDataTitle];
//       if (categoryName) {
//         slugParts.unshift(categoryName);
//       }
//       return format(slugParts.join('/'));
//     }

//     return value
//   }

export default formatSlug
