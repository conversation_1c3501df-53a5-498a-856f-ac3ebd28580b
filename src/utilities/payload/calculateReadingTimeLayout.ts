import { FieldHook } from 'payload'
import { countTotalWords } from '../local/blog'

const calculateReadingTimeHook =
  (fallback: string): FieldHook =>
  ({ value, originalDoc, data }) => {
    if (value) {
      return value
    }

    const fallbackData = data?.[fallback] || originalDoc?.[fallback]

    //   Fallback data: [
    // {
    //   id: '686bb1e0df99906dd23a4488',
    //   richText: { root: [Object] },
    //   blockType: 'content',
    //   blockName: undefined
    // }

    console.log('Fallback data:', fallbackData)

    let readingTime = 0

    for (const data of fallbackData) {
      if (data.blockType === 'content') {
        readingTime += countTotalWords(data.richText.root.children)
      }
    }

    readingTime = Math.round(Math.max(readingTime / 200, 1))

    return readingTime
  }

export default calculateReadingTimeHook
