export function formatTimestamp(timestamp: string | null | undefined): string {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const day = date.getUTCDate()
  const month = date.toLocaleString('en-US', { month: 'long' })
  const year = date.getUTCFullYear()
  return `${day} ${month} ${year}`
}

export const calculateNextMonthlyCreditRenewal = (startDate: string, endDate: string) => {
  const subscriptionStart = new Date(startDate)
  const subscriptionEnd = new Date(endDate)
  const now = new Date()

  // Start from the subscription start date
  let nextRenewalDate = new Date(subscriptionStart)

  // Keep adding months until we find the next renewal date after today
  while (nextRenewalDate <= now) {
    nextRenewalDate.setMonth(nextRenewalDate.getMonth() + 1)
  }

  // Make sure we don't go past the subscription end date
  if (nextRenewalDate > subscriptionEnd) {
    nextRenewalDate = subscriptionEnd
  }

  return nextRenewalDate.toLocaleDateString(undefined, {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  })
}
