services:
  web-app:
    # user: "1001:1001"
    image: app:v0.0.1
    # build:
    #   context: .
    #   dockerfile: Dockerfile
    #   network: host
    #   args:
    #     # note this strictly from .env file. Alternatively use build-args with compose build
    #     PG_USERNAME: ${PG_USERNAME}
    #     PG_PASSWORD: ${PG_PASSWORD}
    #     PG_HOST: ${PG_HOST}
    #     PG_PORT: ${PG_PORT}
    #     PG_DATABASE: ${PG_DATABASE}
    # volumes:
    #   - data:/app/generated-images

    env_file:
      - .env.production
    labels:
      - traefik.enable=true
      - traefik.http.routers.web-app.rule=Host(`www.coloraria.com`) || Host(`coloraria.com`)
      - traefik.http.routers.web-app.entrypoints=https
      - traefik.http.routers.web-app.tls=true
      - traefik.http.routers.web-app.tls.certresolver=letsencrypt
      - traefik.http.services.web-app.loadbalancer.server.port=80
    networks:
      - traefik-net

networks:
  traefik-net:
    external: true

# volumes:
#   data:
