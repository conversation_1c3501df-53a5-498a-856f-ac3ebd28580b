services:
  web-app:
    image: node-app:v0.0.1
    build:
      context: .
      dockerfile: Dockerfile
      network: host
      args:
        # note this strictly from .env file. Alternatively use build-args with compose build
        PG_USERNAME: ${PG_USERNAME}
        PG_PASSWORD: ${PG_PASSWORD}
        PG_HOST: ${PG_HOST}
        PG_PORT: ${PG_PORT}
        PG_DATABASE: ${PG_DATABASE}
    volumes:
      - data:/app/generated-images

volumes:
  data:
