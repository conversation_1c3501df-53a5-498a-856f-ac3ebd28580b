#!/bin/bash

FOLDER_NAME="line-art-generator-v2"
REPO_URL="**************:proriderless/line-art-generator-v2.git"

# assume folder exists for subsequent deployment, folder need to be setup initially with secrets
echo "Folder '$FOLDER_NAME' found. Navigating and updating..."
cd "$FOLDER_NAME" || { echo "Error: Failed to change directory. Exiting."; exit 1; }
git checkout master
if [ $? -ne 0 ]; then
echo "Error: Failed to checkout master branch. Exiting."
exit 1
fi
git pull
if [ $? -ne 0 ]; then
echo "Error: Failed to pull latest changes. Exiting."
exit 1
fi

docker compose -f docker-compose.postgres.yml up -d 
docker compose -f docker-compose.traefik.yml up -d
docker compose -f docker-compose.prod.yml up -d --build
docker image prune -f
