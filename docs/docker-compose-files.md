
## For dev
docker-compose.yml: postgres + app, this do not run next build, suitable for dev with hot-reload


## Local build test
docker-compose.postgres.yml: pre-requisite for app build
docker-compose.traefik.yml: to test built app on local
docker-compose.test.yml: builds the image and runs the app on local with reverse proxy

## For CI
docker-compose.ci-postgres.yml: pre-requisite for ci-app
docker-compose.ci-app.yml: builds the image and runs the app, to test build is fine. Note: tried directly replaced with docker build + build args but failed.

## For production (ideally none of following services should stop running)
docker-compose.postgres.yml: pre-requisite for app build
docker-compose.traefik.yml: start reverse proxy server
docker-compose.prod.yml: builds the image and runs the app with reverse proxy
