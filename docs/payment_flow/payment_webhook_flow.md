# Payment Webhook Flow

Full documentation & proposed implementation:
https://docs.stripe.com/billing/subscriptions/overview#how-payments-work-with-subscriptions
## When using checkout session for the initial charge:

**Successful flow:**
1. customer.created
2. customer.updated
3. payment_intent.created
4. charge.succeeded
5. payment_method.attached
6. payment_intent.succeeded
7. invoice.created
8. invoice.finalized
9. invoice.paid
10. invoice.payment.succeeded
11. customer.subscription.created
12. checkout.session.completed

**Declined Flow:**
1. customer.created
2. customer.updated
3. payment_intent.created
4. charge.failed
5. payment_intent.payment_failed

**Inside payment_failed:**

```ts
{
  "object": {
    "id": "pi_3RmRZjD6QmyzADx803VzR7X8",
    "object": "payment_intent",
    "amount": 999,
    "amount_capturable": 0,
    "amount_details": {
      "tip": {}
    },
    "amount_received": 0,
    "application": null,
    "application_fee_amount": null,
    "automatic_payment_methods": null,
    "canceled_at": null,
    "cancellation_reason": null,
    "capture_method": "automatic",
    "client_secret": "pi_3RmRZjD6QmyzADx803VzR7X8_secret_OIKO1etKLXbK66mp8IgsMLSGt",
    "confirmation_method": "automatic",
    "created": 1752896199,
    "currency": "usd",
    "customer": "cus_ShrIsYrqJ876XO",
    "description": "Subscription creation",
    "last_payment_error": {
      "advice_code": "try_again_later",
      "charge": "ch_3RmRZjD6QmyzADx80rnsZsYm",
      "code": "card_declined",
      "decline_code": "generic_decline",
      "doc_url": "https://stripe.com/docs/error-codes/card-declined",
      "message": "Your card was declined.",
      "payment_method": {
        "id": "pm_1RmRZiD6QmyzADx8l58Qn4fJ",
        "object": "payment_method",
        "allow_redisplay": "limited",
        "billing_details": {
          "address": {
            "city": null,
            "country": "SG",
            "line1": null,
            "line2": null,
            "postal_code": null,
            "state": null
          },
          "email": "<EMAIL>",
          "name": "yt",
          "phone": null,
          "tax_id": null
        },
        "card": {
          "brand": "visa",
          "checks": {
            "address_line1_check": null,
            "address_postal_code_check": null,
            "cvc_check": "pass"
          },
          "country": "US",
          "display_brand": "visa",
          "exp_month": 12,
          "exp_year": 2029,
          "fingerprint": "QXe3R6DuPlSmopzY",
          "funding": "credit",
          "generated_from": null,
          "last4": "0002",
          "networks": {
            "available": [
              "visa"
            ],
            "preferred": null
          },
          "regulated_status": "unregulated",
          "three_d_secure_usage": {
            "supported": true
          },
          "wallet": null
        },
        "created": 1752896198,
        "customer": null,
        "livemode": false,
        "metadata": {},
        "type": "card"
      },
      "type": "card_error"
    },
    "latest_charge": "ch_3RmRZjD6QmyzADx80rnsZsYm",
    "livemode": false,
    "metadata": {},
    "next_action": null,
    "on_behalf_of": null,
    "payment_method": null,
    "payment_method_configuration_details": null,
    "payment_method_options": {
      "card": {
        "installments": null,
        "mandate_options": null,
        "network": null,
        "request_three_d_secure": "automatic",
        "setup_future_usage": "off_session"
      }
    },
    "payment_method_types": [
      "card"
    ],
    "processing": null,
    "receipt_email": null,
    "review": null,
    "setup_future_usage": "off_session",
    "shipping": null,
    "source": null,
    "statement_descriptor": null,
    "statement_descriptor_suffix": null,
    "status": "requires_payment_method",
    "transfer_data": null,
    "transfer_group": null
  },
  "previous_attributes": null
}
```

#### CONCLUSION:

For our case, when using checkout session at the initial stage, we just need to check for one event:
- checkout.session.completed
### Proposed Minimal flow (Checkout session):

1. When user initiate checkout session, we create a SubscriptionRecord on our end with STATUS: Incomplete
2. When checkout.session.completed is fired, we should receive the `subscriptionRecordId` from the metadata, and we use that information to update the subscription record and setting it to active.
3. After step 2, we will also assign this SubscriptionRecord to the corresponding PublicUser.
4. checkout.session.completed also contains stripe's subscription id, so if we need more info, we can check.

**For possible failed case:**

We DO NOT need to handle the failed case, as the checkout session will handle this for us.

# When using collecting future payments:

Does the subscription created time affect when a payment is collected?
**YES.**

![check_time](image_reference/check_time.png)

So for ANNUAL charges when it comes to refreshing credits, we need to consider the time as well.

From observation, we can use current_period_start as reference, as the time recorded is exactly the same.

# EXTREMELY IMPORTANT (Not super sure how timezone conversion is handled on stripe's side):

Since we're in Singapore, the time in Stripe is ALSO GMT+8 (UTC+8), so all created time are in GMT+8, in lieu of this, we should ensure that our server clock environment is set to GMT+8 as well to ensure our SubscriptionRecord time syncs with Stripe's time. 
### Successful Subscription continuation - Webhooks Fired (in order):

1. customer.updated
2. invoice.upcoming
3. invoice.created
4. customer.subscription.updated
5. charge.succeeded
6. payment_intent.succeeded
7. payment_intent.created (for future)
8. invoice.updated
9. invoice.finalized
10. **invoice.paid** *(What we'll exclusively listen to)*
11. invoice.payment_succeeded
12. invoice_payment.paid

From the order, we should be listening to **invoice.paid** to check if the customer has successfully paid for the service.

Payload stripe sends:
```ts
{
  "object": {
    "id": "in_1RmTpTD6QmyzADx8diF8zE97",
    "object": "invoice",
    "account_country": "SG",
    "account_name": "Default sandbox",
    "account_tax_ids": null,
    "amount_due": 999,
    "amount_overpaid": 0,
    "amount_paid": 999,
    "amount_remaining": 0,
    "amount_shipping": 0,
    "application": null,
    "attempt_count": 1,
    "attempted": true,
    "auto_advance": false,
    "automatic_tax": {
      "disabled_reason": null,
      "enabled": false,
      "liability": null,
      "provider": null,
      "status": null
    },
    "automatically_finalizes_at": null,
    "billing_reason": "subscription_cycle",
    "collection_method": "charge_automatically",
    "created": **********,
    "currency": "usd",
    "custom_fields": null,
    "customer": "cus_ShXKf7exqVe6cl",
    "customer_address": {
      "city": null,
      "country": "SG",
      "line1": null,
      "line2": null,
      "postal_code": null,
      "state": null
    },
    "customer_email": "<EMAIL>",
    "customer_name": "ef",
    "customer_phone": null,
    "customer_shipping": null,
    "customer_tax_exempt": "none",
    "customer_tax_ids": [],
    "default_payment_method": null,
    "default_source": null,
    "default_tax_rates": [],
    "description": null,
    "discounts": [],
    "due_date": null,
    "effective_at": **********,
    "ending_balance": 0,
    "footer": null,
    "from_invoice": null,
    "hosted_invoice_url": "https://invoice.stripe.com/i/acct_1R2lnaD6QmyzADx8/test_YWNjdF8xUjJsbmFENlFteXpBRHg4LF9TaHRjQURibURYZTIzU0J0Y0p4emRnc25JV1RhcEJSLDE0MzQ0NTY3MA0200C0CoSZeh?s=ap",
    "invoice_pdf": "https://pay.stripe.com/invoice/acct_1R2lnaD6QmyzADx8/test_YWNjdF8xUjJsbmFENlFteXpBRHg4LF9TaHRjQURibURYZTIzU0J0Y0p4emRnc25JV1RhcEJSLDE0MzQ0NTY3MA0200C0CoSZeh/pdf?s=ap",
    "issuer": {
      "type": "self"
    },
    "last_finalization_error": null,
    "latest_revision": null,
    "lines": {
      "object": "list",
      "data": [
        {
          "id": "il_1S8bnnD6QmyzADx8sho8e0JI",
          "object": "line_item",
          "amount": 999,
          "currency": "usd",
          "description": "1 × Prelude Plan (at $9.99 / month)",
          "discount_amounts": [],
          "discountable": true,
          "discounts": [],
          "invoice": "in_1RmTpTD6QmyzADx8diF8zE97",
          "livemode": false,
          "metadata": {},
          "parent": {
            "invoice_item_details": null,
            "subscription_item_details": {
              "invoice_item": null,
              "proration": false,
              "proration_details": {
                "credited_items": null
              },
              "subscription": "sub_1Rm8FpD6QmyzADx8KaIYnERc",
              "subscription_item": "si_ShXKyfwx38Ez9m"
            },
            "type": "subscription_item_details"
          },
          "period": {
            "end": 1760770727,
            "start": **********
          },
          "pretax_credit_amounts": [],
          "pricing": {
            "price_details": {
              "price": "price_1RgQyoD6QmyzADx8UGrH7rsY",
              "product": "prod_SZPutfDz2v6AW8"
            },
            "type": "price_details",
            "unit_amount_decimal": "999"
          },
          "quantity": 1,
          "taxes": []
        }
      ],
      "has_more": false,
      "total_count": 1,
      "url": "/v1/invoices/in_1RmTpTD6QmyzADx8diF8zE97/lines"
    },
    "livemode": false,
    "metadata": {},
    "next_payment_attempt": null,
    "number": "GZW8EXPZ-0003",
    "on_behalf_of": null,
    "parent": {
      "quote_details": null,
      "subscription_details": {
        "metadata": {},
        "subscription": "sub_1Rm8FpD6QmyzADx8KaIYnERc"
      },
      "type": "subscription_details"
    },
    "payment_settings": {
      "default_mandate": null,
      "payment_method_options": {
        "acss_debit": null,
        "bancontact": null,
        "card": {
          "request_three_d_secure": "automatic"
        },
        "customer_balance": null,
        "konbini": null,
        "sepa_debit": null,
        "us_bank_account": null
      },
      "payment_method_types": null
    },
    "period_end": **********,
    "period_start": **********,
    "post_payment_credit_notes_amount": 0,
    "pre_payment_credit_notes_amount": 0,
    "receipt_number": null,
    "rendering": null,
    "shipping_cost": null,
    "shipping_details": null,
    "starting_balance": 0,
    "statement_descriptor": null,
    "status": "paid",
    "status_transitions": {
      "finalized_at": **********,
      "marked_uncollectible_at": null,
      "paid_at": **********,
      "voided_at": null
    },
    "subtotal": 999,
    "subtotal_excluding_tax": 999,
    "test_clock": "clock_1RmTizD6QmyzADx8RzTQWWY7",
    "total": 999,
    "total_discount_amounts": [],
    "total_excluding_tax": 999,
    "total_pretax_credit_amounts": [],
    "total_taxes": [],
    "webhooks_delivered_at": **********
  },
  "previous_attributes": null
}
```

### Suggestions on handling Success flow (Monthly):

Given that stripe returns the stripe customer id eg: `cus_ShXKf7exqVe6cl` and subscription id: `sub_1Rm8FpD6QmyzADx8KaIYnERc`, we'll use this to identify which subscription record is this.

Then proceed to update SubscriptionRecord status to Active. (Even if it's already active at the start.)

### IMPORTANT NOTE: 

**invoice.paid webhook is also triggered on the initial charge.**

However, we can check for this as from the initial charge flow, the SubscriptionRecord SHOULD NOT YET have the stripe's subscription id as well as stripe's customer id. Using this, we can identify that the initial charge has yet to be complete, and skip this webhook.

In short, if invoice.paid's stripe customer and subscription id can identify the SubscriptionRecord, we can consider that it's a continuation of a subscription.

## Release of Credits (Monthly):

This will have to be handled on two possible webhooks:
- invoice.paid
- checkout.session.completed

If invoice.paid and checkout.session.completed managed to successfully update the correct SubscriptionRecord, we can successfully release the credits to the user.

### Failed Subscription continuation - Webhooks Fired (in order):

- invoice.upcoming
- invoice.created
- customer.subscription.updated
- charge.failed
- payment_intent.payment_failed
- payment_intent.created
- invoice.updated
- customer.updated
- invoice.finalized
- customer.subscription.updated
- invoice.updated
- invoice.payment_failed
- charge.failed
- payment_intent.payment_failed

**Stripe retries payment 3 more times (Causing the following to be fired 3 times)**
- invoice.updated
- invoice.updated
- invoice.payment_failed
- charge.failed
- payment_intent.payment_failed

**Once Stripe gives up retrying:**
- invoice.payment_failed
- invoice.updated
- invoice.updated
- customer.subscription.deleted
### Suggestions on handling failed flow (Monthly):

We will be using:
- invoice.payment_failed
- customer.subscription.deleted

Given that stripe returns the stripe customer id eg: `cus_ShXKf7exqVe6cl` and subscription id: `sub_1Rm8FpD6QmyzADx8KaIYnERc`, we'll use this to identify which subscription record is this.

1. Proceed to update SubscriptionRecord, setting it to **Past Due** for now, as I believe it's most ideal for our use case.

Once Stripe returns 'customer.subscription.deleted', we can assume this SubscriptionRecord is formally deleted. Then:

1. Proceed to update SubscriptionRecord to **Canceled**
2. Detach the SubscriptionRecord from corresponding PublicUser

Setting it to **Canceled** serves as a 'soft delete' on our end to serve as an audit trail.

Handling it this way also ensures that if Stripe suddenly receives a 'invoice.paid' event, we can set it to '**Active**'

# In Summary:
## Webhooks in total we need to listen to:

- invoice.paid
- invoice.payment_failed
- customer.subscription.updated
- customer.subscription.deleted
- checkout.session.completed

## Suggested DB Changes:

- Change subscriptionId to stripeSubscriptionId so it's clearer
- Change customerId to stripeCustomerId so it's clearer
- Remove stripeCustomerId NOT_NULL, and allow it to be NULL at the start when SubscriptionRecord is created.
