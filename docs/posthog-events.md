# PostHog Event Tracking Documentation

This document outlines all the vital events captured on the API layer for analytics and user behavior tracking using PostHog.

## Overview

All events follow a consistent pattern:
- Events are captured after successful operations (not before)
- Error handling prevents PostHog failures from breaking main functionality
- User identification uses email when available, falls back to user ID
- Events include relevant context properties for analysis

## Event Categories

### 1. User Authentication & Management Events

#### `user_registration`
**Endpoint:** `/api/user/(register)/register`  
**Trigger:** After successful user registration  
**Properties:**
- `firstName` - User's first name
- `lastName` - User's last name  
- `userId` - Generated user ID

#### `email_verification`
**Endpoint:** `/api/user/(register)/verifyCode`  
**Trigger:** After successful email verification  
**Properties:**
- `userId` - User ID
- `firstName` - User's first name
- `lastName` - User's last name

#### `password_login`
**Endpoint:** `/api/user/login`  
**Trigger:** After successful password-based login  
**Properties:** None (existing implementation)

#### `oauth_login`
**Endpoint:** `/api/(oauth)/oauth/google/callback`  
**Trigger:** After successful OAuth authentication  
**Properties:**
- `userId` - User ID
- `firstName` - User's first name
- `lastName` - User's last name
- `provider` - OAuth provider (e.g., "google")

#### `user_logout`
**Endpoint:** `/api/user/logout`  
**Trigger:** Before clearing authentication cookies  
**Properties:**
- `userId` - User ID

#### `profile_update`
**Endpoint:** `/api/user/updateUser`  
**Trigger:** After successful profile update  
**Properties:**
- `userId` - User ID
- `firstName` - Updated first name
- `lastName` - Updated last name

### 2. Payment & Subscription Events

#### `checkout_session_created`
**Endpoint:** `/api/checkout-session`  
**Trigger:** After successful Stripe checkout session creation  
**Properties:**
- `subscriptionName` - Name of subscription plan
- `subscriptionDuration` - Duration (month/year)
- `stripePriceId` - Stripe price ID
- `sessionId` - Stripe session ID

#### `payment_success`
**Endpoint:** `/api/webhooks/stripe` (checkout.session.completed)  
**Trigger:** When Stripe confirms successful payment  
**Properties:**
- `sessionId` - Stripe session ID
- `amountTotal` - Total amount paid
- `currency` - Payment currency
- `paymentStatus` - Payment status from Stripe

#### `recurring_payment_success`
**Endpoint:** `/api/webhooks/stripe` (invoice.paid)
**Trigger:** When recurring subscription payment succeeds
**Properties:**
- `invoice` - Complete Stripe Invoice object containing all invoice details (id, amount_paid, currency, subscription, etc.)

#### `payment_failed`
**Endpoint:** `/api/webhooks/stripe` (invoice.payment_failed)
**Trigger:** When payment attempt fails
**Properties:**
- `invoice` - Complete Stripe Invoice object containing all invoice details (id, amount_due, currency, subscription, etc.)

#### `subscription_updated`
**Endpoint:** `/api/webhooks/stripe` (customer.subscription.updated)
**Trigger:** When subscription details change
**Properties:**
- `subscription` - Complete Stripe Subscription object containing all subscription details (id, status, current_period_end, etc.)

#### `subscription_cancelled`
**Endpoint:** `/api/webhooks/stripe` (customer.subscription.deleted)  
**Trigger:** When subscription is cancelled  
**Properties:**
- `subscriptionId` - Stripe subscription ID
- `canceledAt` - Cancellation timestamp
- `cancelAtPeriodEnd` - Whether cancellation is at period end

### 3. Core Image Generation Events

#### `text_to_image_generation`
**Endpoint:** `/api/image-generation/text-to-image`  
**Trigger:** After initiating text-to-image generation  
**Properties:**
- `prompt` - Text prompt (truncated to 100 chars for privacy)
- `aspectRatio` - Image aspect ratio
- `quality` - Quality setting (LOW_QUALITY/HIGH_QUALITY)
- `numberOfImages` - Number of images requested
- `remainingCredits` - User's remaining credits after deduction

#### `image_to_image_generation`
**Endpoint:** `/api/image-generation/image-to-image`  
**Trigger:** After initiating image-to-image generation  
**Properties:**
- `aspectRatio` - Image aspect ratio
- `quality` - Quality setting
- `numberOfImages` - Number of images requested
- `hasUnsplashSource` - Whether Unsplash image was used as source
- `remainingCredits` - User's remaining credits after deduction

#### `background_removal`
**Endpoint:** `/api/image-generation/remove-background`  
**Trigger:** After initiating background removal  
**Properties:**
- `aspectRatio` - Image aspect ratio
- `remainingCredits` - User's remaining credits after deduction

#### `image_upscale`
**Endpoint:** `/api/image-generation/image-upscale`  
**Trigger:** After successful image upscaling  
**Properties:**
- `upscaleLevel` - Upscale multiplier
- `remainingCredits` - User's remaining credits after deduction

#### `image_contrast_adjustment`
**Endpoint:** `/api/image-generation/image-contrast`  
**Trigger:** After successful contrast adjustment  
**Properties:**
- `imageWidth` - Processed image width
- `imageHeight` - Processed image height

### 4. Content Management Events

#### `image_deleted`
**Endpoint:** `/api/images/[imageId]`  
**Trigger:** After successful image deletion  
**Properties:**
- `imageId` - ID of deleted image

## Implementation Details

### Error Handling
All PostHog tracking calls are wrapped in try-catch blocks:
```typescript
try {
  const posthog = PostHogClient()
  posthog.capture({
    event: 'event_name',
    distinctId: userId,
    properties: { /* event properties */ }
  })
} catch (error) {
  console.error('PostHog tracking error:', error)
}
```

### Type Safety Approach
For Stripe webhook events, we pass complete objects rather than individual properties:
```typescript
// Good: Pass complete object for rich analytics
properties: {
  invoice  // Complete Stripe Invoice object
}

// Avoid: Type casting and individual property extraction
properties: {
  invoiceId: (invoice as any).id,  // Don't do this
  amount: (invoice as any).amount_paid
}
```

### User Identification Strategy
- Primary: Use email address as `distinctId` when available
- Fallback: Use user ID when email is not accessible
- For webhook events: Use Stripe customer ID or client reference ID

### Privacy Considerations
- User prompts are truncated to 100 characters
- Passwords are never tracked
- Sensitive user data is excluded from properties
- Image data (base64) is never included in events
- Stripe webhook events include complete objects for comprehensive analytics while maintaining security through PostHog's data handling

## Usage for Analytics

These events enable tracking of:
- **User Journey:** Registration → Verification → Feature Usage → Retention
- **Conversion Funnel:** Checkout → Payment → Subscription Management
- **Feature Adoption:** Which image generation features are most popular
- **User Engagement:** Frequency and patterns of feature usage
- **Revenue Metrics:** Payment success rates, subscription lifecycle
- **Product Performance:** Credit usage patterns, feature preferences

## Event Naming Convention

Events use `snake_case` naming following PostHog best practices:
- User actions: `user_*` (user_registration, user_logout)
- Payment events: `payment_*` or `subscription_*`
- Feature usage: `feature_name` (text_to_image_generation, background_removal)
