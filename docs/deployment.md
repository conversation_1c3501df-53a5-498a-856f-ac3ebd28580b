
## Production Deployment

server provisioning + application deployment

Prepare environment:
- Docker env
- Code repo

Once running:
- cloudflare point to VPS IP
- VPS need expose 80 and 443
- add required data to DB
- configure webhook (stripe)

Allow SSH:
- Hetzner specific



### SSH key-pairs

Usual generation process:

ssh-keygen -t ed25519 -C "optional comment"


Prompt for location - change key name here, so that it's easier to identify.  
Prompt for passphrase - this is only for private key encryption (in case private key exposed, it serves as password for private key), usually not used.  

This produces a pair of keys:  
<key-name> - private key  
<key-name>.pub - public key


We have 3 key pairs:  

1. <PERSON><PERSON><PERSON>ner key for root user (requires passphrase during ssh-keygen)

Need for server provisioning.  
The public key uploaded on <PERSON><PERSON><PERSON> console, which will be injected into server when they are created.  
With this setup, root password login is default disabled.  
Now can proceed with server provisioning.  
During provisioning, create non-root user with other key-pairs (afterwards should not be using this key most of the time)

Key-Name/Direction: admin-root
server-username: root

2. worker key for non-root user (no-passphrase)

Need for other operations: continuous deployment, debugging on server
Generate this key on local machine, and copy the public key to server .ssh/authorized_keys (few ways to do this, do it during provisioning when the user is created, or use ssh-copy-id)
To use private key in the pipeline, set pipeline secrets SSH_PRIVATE_KEY and SERVER_IPV4 using gh, then make reference to them in the workflow.  

Name/Direction: worker-coloraria
server-username: coloraria

3. Github key for pulling repo (no-passphrase)

Need for repo access.  
Generate this key on server, and add the public key to github.  
Then on the server, need to add Github public key to known_hosts (using ssh-keyscan).  

Refer to the identity file in ~/./ssh/config so that we can directly pull using repo SSH link, without specifying identity file.  
```
Host coloraria-repo
    Hostname github.com
    User git
    IdentityFile ~/.ssh/coloraria-github
    IdentitiesOnly yes
```

Key-Name/Direction: coloraria-github
server-username: git


### Monitoring service


### CD Pipeline
scripts folder - need exists first (with right permission), then CD pipeline can work


### Server Provisioning

Update and upgrade packages (optional)
`sudo apt update && sudo apt upgrade -y`


Add non-root user (coloraria)
`sudo useradd coloraria`
`sudo usermod -aG sudo coloraria`
`sudo usermod -aG docker coloraria`

Generate ssh key-pair for user (no-passphrase)
`ssh-keygen -t ed25519 -C "coloraria-github" < ~/.ssh/coloraria-github`

Create entry in ~/.ssh/config

Add public key to Hetzner console, so that it's injected into server when created.  


### Image build issue
Nextjs build process requires DB connection.
So we need to start postgres before building the image.

And the database connection needs to be available at build time, environment variables in compose file/build time are strictly taken from **.env** file.

`docker network create traefik-net`
`docker compose -f docker-compose.postgres.yml up -d && docker compose -f docker-compose.prod.yml up -d --build`
