import subprocess
import json
from pathlib import Path
import shutil

import modal

from .app import app, vol
from .config import (
    COMFYUI_INPUT_DIR, COMFYUI_OUTPUT_DIR, CONTAINER_CPU, CONTAINER_MEMORY, CONTAINER_GPU, CONTAINER_TIMEOUT,
    MAX_CONTAINERS, SCALEDOWN_WINDOW, MAX_CONCURRENT_INPUTS,
    DEFAULT_WIDTH, DEFAULT_HEIGHT, DEFAULT_BATCH_SIZE, T2I_PROMPT_TEMPLATE,
)
from .utils import (
    check_server_health, log_request_info, extract_object_key, find_node_by_type,
    load_workflow_json, generate_random_seed, cleanup_file, find_output_files
)
from .models import Text2ImageRequestBody, ImagesResponse
from .api_handlers import (
    configure_workflow_common, save_uploaded_image, get_image_dimensions,
    configure_t2i_workflow, configure_i2i_workflow, configure_rmbg_workflow,
    upload_output_image
)
from .image_upload import upload_file_to_r2


####################################Application####################################

@app.cls(
    cpu=CONTAINER_CPU,
    memory=CONTAINER_MEMORY,
    gpu=CONTAINER_GPU,
    timeout=CONTAINER_TIMEOUT,
    volumes={"/cache": vol},
    max_containers=MAX_CONTAINERS,
    scaledown_window=SCALEDOWN_WINDOW,
    enable_memory_snapshot=True,
)
@modal.concurrent(max_inputs=MAX_CONCURRENT_INPUTS)
class ComfyUI:
    port: int = 8000

    @modal.enter(snap=True)
    def launch_comfy_background(self):
        cmd = f"comfy launch --background -- --port {self.port}"
        subprocess.run(cmd, shell=True, check=True)

    @modal.enter(snap=False)
    def restore_snapshot(self):
        # initialize GPU for ComfyUI after snapshot restore
        # note: requires patching core ComfyUI, see the memory_snapshot_helper directory for more details
        import requests

        response = requests.post(f"http://127.0.0.1:{self.port}/cuda/set_device")
        if response.status_code != 200:
            print("Failed to set CUDA device")
        else:
            print("Successfully set CUDA device")

    @modal.method()
    def infer(self, workflow_path: str = "/root/flux_t2i_api.json"):
        # sometimes the ComfyUI server stops responding (we think because of memory leaks), so this makes sure it's still up
        self.poll_server_health()

        # runs the comfy run --workflow command as a subprocess
        cmd = f"comfy run --workflow {workflow_path} --wait --timeout 1200 --verbose"
        # subprocess.run(cmd, shell=True, check=True)

        try:
            result = subprocess.run(
                cmd,  # Replace with your command and arguments
                check=True,  # Raise an exception for non-zero exit codes
                stdout=subprocess.PIPE,      # Capture standard output
                stderr=subprocess.PIPE,      # Capture standard error
                text=True,                  # Return stdout and stderr as strings
                shell=True,
            )
            print("Command executed successfully.")
            print("Standard Output:", result.stdout)
        except subprocess.CalledProcessError as e:
            print(f"Command failed with exit code {e.returncode}.")
            print("Standard Output:", e.stdout)
            print("Standard Error:", e.stderr)
        except FileNotFoundError as e:
            print(f"File or directory not found: {e}")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")

    @modal.asgi_app()
    def image_generation(self):
        from fastapi import FastAPI, Request, HTTPException, UploadFile, File, Form

        web_app = FastAPI()

        @web_app.post("/t2i")
        def t2i(request: Request, body: Text2ImageRequestBody):
            # handle request
            prompt = body.prompt
            batch_size = body.batch_size
            width = body.width
            height = body.height
            image_urls = body.image_urls
            
            # Validate t2i request explicitly
            if not prompt:
                raise HTTPException(status_code=400, detail="Prompt is required for text-to-image generation.")
            
            if len(image_urls) != batch_size:
                raise HTTPException(status_code=400, detail=f"Number of upload URLs ({len(image_urls)}) must match batch size ({batch_size})")

            # Log request using common logging
            client_host = request.client.host if request.client else "unknown"
            log_request_info("t2i", client_host,
                           prompt=prompt, batch_size=batch_size,
                           width=width, height=height)

            # Load workflow using utility function
            main_workflow_data = load_workflow_json(Path(__file__).parent / "flux_t2i_api.json")
            perform_upscale = (width != height) and (width >= 1024 or height >= 1024)
            
            # Format prompt with template
            formatted_prompt = T2I_PROMPT_TEMPLATE.format(prompt=prompt)

            # Generate multiple images with different seeds
            images = []
            temp_files = []
            
            try:
                for i in range(batch_size):
                    image_id = extract_object_key(image_urls[i])

                    if perform_upscale:
                        # Configure workflow using api_handlers function
                        configured_workflow = configure_t2i_workflow(
                            main_workflow_data.copy(),
                            formatted_prompt, 
                            width//2, 
                            height//2, 
                            image_id
                        )
                        
                        # Create temporary workflow file
                        temp_workflow_file = f"{image_id}.json"
                        temp_files.append(Path(temp_workflow_file))
                        json.dump(configured_workflow, Path(temp_workflow_file).open("w"))

                        # Run inference
                        self.infer.local(temp_workflow_file)


                        upscale_workflow_data = load_workflow_json(Path(__file__).parent / "t2i_upscale_api.json")

                        comfy_input_dir = Path(COMFYUI_INPUT_DIR)
                        comfy_output_dir = Path(COMFYUI_OUTPUT_DIR)
                        
                        # Find the actual output file using the image_id prefix
                        output_files = find_output_files(comfy_output_dir, image_id)
                        if output_files:
                            source_path = output_files[0]  # Use the first matching file
                            image_filename = source_path.name
                            destination_path = comfy_input_dir / image_filename
                            shutil.copy(source_path, destination_path)
                        else:
                            raise Exception(f"No output file found with prefix {image_id}")

                        upscale_workflow_data["1"]["inputs"]["image"] = image_filename
                        upscale_workflow_data["2"]["inputs"]["rescale_factor"] = 2
                        upscale_workflow_data["2"]["inputs"]["resize_width"] = width
                        upscale_workflow_data["2"]["inputs"]["resize_height"] = height
                        configured_upscale_workflow = configure_workflow_common(
                            upscale_workflow_data, image_id, save_node_type="SaveImage"
                        )

                        json.dump(configured_upscale_workflow, Path(temp_workflow_file).open("w"))
                        self.infer.local(temp_workflow_file)
                    
                    else:
                        # Configure workflow using api_handlers function
                        configured_workflow = configure_t2i_workflow(
                            main_workflow_data.copy(),
                            formatted_prompt, 
                            width, 
                            height, 
                            image_id
                        )
                        
                        # Create temporary workflow file
                        temp_workflow_file = f"{image_id}.json"
                        temp_files.append(Path(temp_workflow_file))
                        json.dump(configured_workflow, Path(temp_workflow_file).open("w"))

                        # Run inference
                        self.infer.local(temp_workflow_file)

                    # Upload output image using api_handlers function
                    metadata = upload_output_image(image_id, image_urls[i], width, height)
                    if metadata:
                        images.append(metadata)
            finally:
                # Clean up temporary workflow files
                for temp_file in temp_files:
                    cleanup_file(temp_file)
   
            return ImagesResponse(images=images)

        @web_app.post("/i2i")
        def i2i(
            request: Request, 
            image: UploadFile = File(...), 
            image_urls: str = Form(""),
            batch_size: int = Form(DEFAULT_BATCH_SIZE, ge=1),
            width: int = Form(DEFAULT_WIDTH, ge=1),
            height: int = Form(DEFAULT_HEIGHT, ge=1),
        ):
            # Parse image URLs
            image_urls_list = image_urls.split(",")
            
            # Validate i2i request explicitly
            if not image:
                raise HTTPException(status_code=400, detail="Image file is required for image-to-image.")
            
            if len(image_urls_list) != batch_size:
                raise HTTPException(status_code=400, detail=f"Number of upload URLs ({len(image_urls_list)}) must match batch size ({batch_size})")

            # Log request using common logging
            client_host = request.client.host if request.client else "unknown"
            log_request_info("i2i", client_host,
                           prompt=None, batch_size=batch_size,
                           width=width, height=height)

            # Save uploaded image using api_handlers function
            image_filename, destination_image_path = save_uploaded_image(image)
            
            # Load workflow using utility function
            workflow_data = load_workflow_json(Path(__file__).parent / "i2i_v3_api.json")

            # Generate multiple images with different seeds
            images = []
            temp_files = []
            
            try:
                for i in range(batch_size):
                    image_id = extract_object_key(image_urls_list[i])
                    
                    # Configure workflow using api_handlers function
                    configured_workflow = configure_i2i_workflow(
                        workflow_data.copy(), 
                        image_filename, 
                        image_id
                    )
                    
                    # Create temporary workflow file
                    temp_workflow_file = f"{image_id}.json"
                    temp_files.append(Path(temp_workflow_file))
                    json.dump(configured_workflow, Path(temp_workflow_file).open("w"))

                    # Run inference
                    self.infer.local(temp_workflow_file)

                    # Upload output image using api_handlers function
                    metadata = upload_output_image(image_id, image_urls_list[i], width, height)
                    if metadata:
                        images.append(metadata)
            finally:
                # Clean up temporary workflow files
                for temp_file in temp_files:
                    cleanup_file(temp_file)
                
                # Clean up the uploaded image file
                cleanup_file(destination_image_path)
                
            return ImagesResponse(images=images)


        @web_app.post("/rmbg")
        def rmbg(
            request: Request, 
            image: UploadFile = File(...),
            image_url: str = Form(""),
        ):
            # Validate rmbg request explicitly
            if not image:
                raise HTTPException(status_code=400, detail="Image file is required for background removal.")
            
            if not image_url:
                raise HTTPException(status_code=400, detail="Upload url is required for background removal.")

            # Log request using common logging
            client_host = request.client.host if request.client else "unknown"
            log_request_info("rmbg", client_host)

            # Save uploaded image using api_handlers function
            image_filename, destination_image_path = save_uploaded_image(image)
            
            # Get image dimensions using api_handlers function
            width, height = get_image_dimensions(destination_image_path)
            
            # Load workflow using utility function
            workflow_data = load_workflow_json(Path(__file__).parent / "rmbg_api.json")
            
            image_id = extract_object_key(image_url)
            
            # Configure workflow using api_handlers function
            configured_workflow = configure_rmbg_workflow(
                workflow_data, 
                image_filename, 
                image_id
            )
            
            # Create temporary workflow file
            temp_workflow_file = f"{image_id}.json"
            temp_workflow_path = Path(temp_workflow_file)
            
            try:
                json.dump(configured_workflow, temp_workflow_path.open("w"))

                # Run inference
                self.infer.local(temp_workflow_file)

                # Upload output image using api_handlers function
                metadata = upload_output_image(image_id, image_url, width, height)
                images = [metadata] if metadata else []
            finally:
                # Clean up temporary workflow file
                cleanup_file(temp_workflow_path)
                
                # Clean up the uploaded image file
                cleanup_file(destination_image_path)

            return ImagesResponse(images=images)

        return web_app


    def poll_server_health(self) -> None:
        """Check if the ComfyUI server is healthy and responding."""
        if not check_server_health(self.port):
            print("ComfyUI server is not healthy, stopping container")
            modal.experimental.stop_fetching_inputs()
            raise Exception("ComfyUI server is not healthy, stopping container")
