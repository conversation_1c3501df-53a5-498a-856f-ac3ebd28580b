import modal
from pathlib import Path
import subprocess

image = (  # build up a Modal Image to run ComfyUI, step by step
    modal.Image.debian_slim(  # start from basic Linux with Python
        python_version="3.11"
    )
    .apt_install("git")  # install git to clone ComfyUI
    .pip_install("fastapi[standard]==0.115.4")  # install web dependencies
    .pip_install("comfy-cli==1.3.8")  # install comfy-cli
    .run_commands(  # use comfy-cli to install ComfyUI and its dependencies
        "comfy --skip-prompt install --fast-deps --nvidia --version 0.3.26"
    )
)

image = (
    image.run_commands(
        "comfy node registry-install comfyui-easy-use"
    )
    .run_commands(
        "comfy node registry-install comfyui_controlnet_aux"
    )
    .run_commands(
        "comfy node registry-install comfyui_ipadapter_plus"
    )
    .run_commands(
        "comfy node registry-install comfyui-custom-scripts"
    )
    .run_commands(
        "comfy node registry-install was-node-suite-comfyui"
    )
    .run_commands(
        "cd /root/comfy/ComfyUI/models && mkdir ipadapter blip && mkdir ipadapter/SDXL checkpoints/SDXL vae/SDXL"
    )
)

image = image.add_local_dir(
    local_path=Path(__file__).parent / "memory_snapshot_helper",
    remote_path="/root/comfy/ComfyUI/custom_nodes/memory_snapshot_helper",
    copy=True,
).add_local_dir(
    local_path=Path(__file__).parent / "ComfyMath",
    remote_path="/root/comfy/ComfyUI/custom_nodes/ComfyMath",
    copy=True,
).add_local_dir(
    local_path=Path(__file__).parent / "sdxl_prompt_styler",
    remote_path="/root/comfy/ComfyUI/custom_nodes/sdxl_prompt_styler",
    copy=True,
)

def hf_download():
    from huggingface_hub import hf_hub_download

    flux_model = hf_hub_download(
        repo_id="Comfy-Org/flux1-schnell",
        filename="flux1-schnell-fp8.safetensors",
        cache_dir="/cache",
    )

    # symlink the model to the right ComfyUI directory
    subprocess.run(
        f"ln -s {flux_model} /root/comfy/ComfyUI/models/checkpoints/flux1-schnell-fp8.safetensors",
        shell=True,
        check=True,
    )

    clip_vision_model = hf_hub_download(
        repo_id="Comfy-Org/sigclip_vision_384",
        filename="sigclip_vision_patch14_384.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {clip_vision_model} /root/comfy/ComfyUI/models/clip_vision/sigclip_vision_patch14_384.safetensors",
        shell=True,
        check=True,
    )

    redux_model = hf_hub_download(
        repo_id="black-forest-labs/FLUX.1-Redux-dev",
        filename="flux1-redux-dev.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {redux_model} /root/comfy/ComfyUI/models/style_models/flux1-redux-dev.safetensors",
        shell=True,
        check=True,
    )

    vae_model = hf_hub_download(
        repo_id="black-forest-labs/FLUX.1-schnell",
        filename="ae.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {vae_model} /root/comfy/ComfyUI/models/vae/ae.safetensors",
        shell=True,
        check=True,
    )

    t5xxl_model = hf_hub_download(
        repo_id="comfyanonymous/flux_text_encoders",
        filename="t5xxl_fp16.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {t5xxl_model} /root/comfy/ComfyUI/models/text_encoders/t5xxl_fp16.safetensors",
        shell=True,
        check=True,
    )

    clipl_model = hf_hub_download(
        repo_id="comfyanonymous/flux_text_encoders",
        filename="clip_l.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {clipl_model} /root/comfy/ComfyUI/models/text_encoders/clip_l.safetensors",
        shell=True,
        check=True,
    )

    lora_depth = hf_hub_download(
        repo_id="stabilityai/control-lora",
        filename="control-LoRAs-rank256/control-lora-depth-rank256.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {lora_depth} /root/comfy/ComfyUI/models/controlnet/control-lora-depth-rank256.safetensors",
        shell=True,
        check=True,
    )

    lora_canny = hf_hub_download(
        repo_id="stabilityai/control-lora",
        filename="control-LoRAs-rank256/control-lora-canny-rank256.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {lora_canny} /root/comfy/ComfyUI/models/controlnet/control-lora-canny-rank256.safetensors",
        shell=True,
        check=True,
    )

    sdxl_ip_adapter = hf_hub_download(
        repo_id="h94/IP-Adapter",
        filename="sdxl_models/ip-adapter_sdxl.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {sdxl_ip_adapter} /root/comfy/ComfyUI/models/ipadapter/SDXL/ip-adapter_sdxl.safetensors",
        shell=True,
        check=True,
    )

    clip_vision = hf_hub_download(
        repo_id="XuminYu/example_safetensors",
        filename="CLIP-ViT-bigG-14-laion2B-39B-b160k.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {clip_vision} /root/comfy/ComfyUI/models/clip_vision/CLIP-ViT-bigG-14-laion2B-39B-b160k.safetensors",
        shell=True,
        check=True,
    )

    sdxl_base = hf_hub_download(
        repo_id="stabilityai/stable-diffusion-xl-base-1.0",
        filename="sd_xl_base_1.0.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {sdxl_base} /root/comfy/ComfyUI/models/checkpoints/SDXL/sd_xl_base_1.0.safetensors",
        shell=True,
        check=True,
    )

    sdxl_vae = hf_hub_download(
        repo_id="stabilityai/sdxl-vae",
        filename="sdxl_vae.safetensors",
        cache_dir="/cache",
    )

    subprocess.run(
        f"ln -s {sdxl_vae} /root/comfy/ComfyUI/models/vae/SDXL/sdxl_vae.safetensors",
        shell=True,
        check=True,
    )

    subprocess.run(
        f"ln -s /cache/models--Salesforce--blip-image-captioning-base /root/comfy/ComfyUI/models/blip/models--Salesforce--blip-image-captioning-base",
        shell=True,
        check=True,
    )

    subprocess.run(
        f"ln -s /cache/models--Salesforce--blip-vqa-base /root/comfy/ComfyUI/models/blip/models--Salesforce--blip-vqa-base",
        shell=True,
        check=True,
    )

    subprocess.run(
        f"ln -s /cache/c0l0ringb00k_Flux_v1_renderartist.safetensors /root/comfy/ComfyUI/models/loras/c0l0ringb00k_Flux_v1_renderartist.safetensors",
        shell=True,
        check=True,
    )

    subprocess.run(
        f"ln -s /cache/flux1-dev-fp8.safetensors /root/comfy/ComfyUI/models/checkpoints/flux1-dev-fp8.safetensors",
        shell=True,
        check=True,
    )


vol = modal.Volume.from_name("hf-hub-cache", create_if_missing=True)

image = (
    # install huggingface_hub with hf_transfer support to speed up downloads
    image.pip_install("huggingface_hub[hf_transfer]==0.30.0")
    .env({"HF_HUB_ENABLE_HF_TRANSFER": "1"})
    .run_function(
        hf_download,
        # persist the HF cache to a Modal Volume so future runs don't re-download models
        volumes={"/cache": vol},
    )
)

image = image.add_local_file(
    Path(__file__).parent / "flux_t2i_api.json", "/root/flux_t2i_api.json"
)
image = image.add_local_file(
    Path(__file__).parent / "i2i_v3_api.json", "/root/i2i_v3_api.json"
)
image = image.add_local_file(
    Path(__file__).parent / "rmbg_api.json", "/root/rmbg_api.json"
)
image = image.add_local_file(
    Path(__file__).parent / "t2i_upscale_api.json", "/root/t2i_upscale_api.json"
)

app = modal.App(name="example-comfyui", image=image)
