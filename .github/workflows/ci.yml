# This workflow only test image can be built.
name: Continue Integration

on:
  pull_request:
    branches: [ "master", "dev" ]

env:
  PG_USERNAME: postgres
  PG_PASSWORD: testpass
  PG_HOST: localhost
  PG_PORT: 5432
  PG_DATABASE: line-art-generator

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Run docker compose
      uses: hoverkraft-tech/compose-action@v2.0.1
      with:
        compose-file: "./docker-compose.ci-postgres.yml"
    - name: Run docker compose
      uses: hoverkraft-tech/compose-action@v2.0.1
      with:
        compose-file: "./docker-compose.ci-app.yml"

# docker network create traefik-net

# docker run -d \
  # --name postgres \
  # --network traefik-net \
  # -e POSTGRES_PASSWORD=mysecretpassword \
  # postgres

# docker buildx create --name image-builder --driver docker-container --use --driver-opt network=traefik-net

# docker buildx inspect --bootstrap

# docker buildx build --add-host=postgres:$(docker inspect -f '{{(index .NetworkSettings.Networks "traefik-net").IPAddress}}' postgres) --build-arg PG_USERNAME=postgres --build-arg PG_PASSWORD=admin1234 --build-arg PG_HOST=postgres --build-arg PG_PORT=5432 --build-arg PG_DATABASE=line-art-generator -t ${{ secrets.DOCKER_USERNAME }}/coloraria:v0.0.1 .
