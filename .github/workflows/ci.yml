# This workflow only test image can be built.
name: Continue Integration

on:
  pull_request:
    branches: [ "master", "dev" ]

env:
  PG_USERNAME: postgres
  PG_PASSWORD: testpass
  PG_HOST: localhost
  PG_PORT: 5432
  PG_DATABASE: line-art-generator
  

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Run docker compose
      uses: hoverkraft-tech/compose-action@v2.0.1
      with:
        compose-file: "./docker-compose.ci-postgres.yml"
    - name: Run docker compose
      uses: hoverkraft-tech/compose-action@v2.0.1
      with:
        compose-file: "./docker-compose.ci-app.yml"

    - name: Create Docker network
      run: docker network create traefik-net

    - name: Run PostgreSQL container
      run: |
        docker run -d \
          --name postgres \
          --network traefik-net \
          -e POSTGRES_PASSWORD=${{ env.PG_PASSWORD }} \
          postgres

    - name: Create Docker buildx builder
      run: |
        docker buildx create --name image-builder --driver docker-container --use --driver-opt network=traefik-net

    - name: Bootstrap buildx builder
      run: docker buildx inspect --bootstrap

    - name: Build Docker image with buildx
      run: |
        POSTGRES_IP=$(docker inspect -f '{{(index .NetworkSettings.Networks "traefik-net").IPAddress}}' postgres)
        docker buildx build \
          --add-host=postgres:$POSTGRES_IP \
          --build-arg PG_USERNAME=${{ env.PG_USERNAME }} \
          --build-arg PG_PASSWORD=${{ env.PG_PASSWORD }} \
          --build-arg PG_HOST=${{ env.PG_HOST }} \
          --build-arg PG_PORT=${{ env.PG_PORT }} \
          --build-arg PG_DATABASE=${{ env.PG_DATABASE }} \
          -t ${{ vars.DOCKER_USERNAME }}/coloraria:v0.0.1 .
