ARG PG_USERNAME
ARG PG_PASSWORD
ARG PG_HOST
ARG PG_PORT
ARG PG_DATABASE

# From https://github.com/vercel/next.js/blob/canary/examples/with-docker/Dockerfile
FROM node:22-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi


# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules

COPY public public
COPY assets assets

COPY src src

COPY package.json package.json
COPY tsconfig.json tsconfig.json
COPY next.config.mjs next.config.mjs
COPY postcss.config.js postcss.config.js
# COPY tailwind.config.js tailwind.config.js
COPY pnpm-lock.yaml pnpm-lock.yaml

ENV NODE_ENV production
ENV NEXT_PUBLIC_HOST https://www.coloraria.com
ENV NEXT_PUBLIC_RECAPTCHA_KEY_ID 6LfvLlorAAAAAPDiWTulhtRKFuujfKaR2YYzo5Q-
ENV NEXT_PUBLIC_POSTHOG_KEY phc_1dd8iqXdRedejlLBDMWheCQTSXWwvmP0QS7nACzFifh
ENV NEXT_PUBLIC_POSTHOG_HOST https://eu.i.posthog.com
# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
ENV NEXT_TELEMETRY_DISABLED 1
# need this key to build
ENV STRIPE_SECRET_KEY placeholder
ENV PAYLOAD_SECRET placeholder
ARG PG_USERNAME
ARG PG_PASSWORD
ARG PG_HOST
ARG PG_PORT
ARG PG_DATABASE
ENV PG_USERNAME=${PG_USERNAME}
ENV PG_PASSWORD=${PG_PASSWORD}
ENV PG_HOST=${PG_HOST}
ENV PG_PORT=${PG_PORT}
ENV PG_DATABASE=${PG_DATABASE}

RUN \
  if [ -f yarn.lock ]; then yarn run build; \
  elif [ -f package-lock.json ]; then npm run build; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm run build; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
# Uncomment the following line in case you want to disable telemetry during runtime.
ENV NEXT_TELEMETRY_DISABLED 1

# TODO disable create user, non-root cannot access volume
# RUN addgroup --system --gid 1001 nodejs
# RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
# RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
# COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
# COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# RUN mkdir /app/generated-images
# RUN chown nextjs:nodejs /app/generated-images
# USER nextjs
# VOLUME /app/generated-images

EXPOSE 80
ENV PORT 80

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD HOSTNAME="0.0.0.0" node server.js
